{"name": "lbs-proxy", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --forceExit", "test:unit": "jest --testPathPattern=src", "test:route-comparison": "jest --config ./test/jest-e2e.json --testPathPattern=route-comparison.spec.ts --verbose", "test:integration": "jest --testPathPattern=integration --verbose", "test:integration:watch": "jest --testPathPattern=integration --watch --verbose", "test:integration:debug": "jest --testPathPattern=integration --verbose --runInBand", "test:route-api-comparison": "jest --testPathPattern=route-comparison.integration.spec.ts --verbose --runInBand"}, "dependencies": {"@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.4.0", "axios": "^1.7.4", "cache-manager": "^5.7.6", "js-yaml": "^4.1.0", "ml-kmeans": "^6.0.0", "nest-winston": "^1.9.7", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "urlencode": "^2.0.0", "uuid": "^11.0.2", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testMatch": ["**/src/**/*.spec.ts", "**/test/**/*.spec.ts"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.spec.ts", "!src/**/*.d.ts"], "coverageDirectory": "coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}, "testTimeout": 30000, "forceExit": true}}