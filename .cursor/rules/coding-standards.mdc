---
description: 
globs: 
alwaysApply: true
---
# Coding Standards and Best Practices

## TypeScript Configuration
- Follow the configuration in [tsconfig.json](mdc:tsconfig.json)
- Use strict mode for type checking
- Enable `strictNullChecks` and `noImplicitAny`
- Prefer explicit return types for public methods

## NestJS Patterns

### Module Structure
```typescript
@Module({
  imports: [ConfigModule, CacheModule],
  controllers: [FeatureController],
  providers: [FeatureService, FeatureRepository],
  exports: [FeatureService] // Only export what other modules need
})
export class FeatureModule {}
```

### Service Implementation
```typescript
@Injectable()
export class FeatureService {
  constructor(
    private readonly apiService: ApiService,
    private readonly logger: Logger
  ) {}

  async methodName(dto: InputDto): Promise<OutputDto> {
    // Implementation with proper error handling
  }
}
```

### Controller Structure
```typescript
@Controller('feature')
@ApiTags('Feature Management')
export class FeatureController {
  @Post()
  @ApiOperation({ summary: 'Create feature' })
  @ApiResponse({ status: 201, type: OutputDto })
  async create(@Body() dto: CreateFeatureDto): Promise<OutputDto> {
    return this.featureService.create(dto);
  }
}
```

## Error Handling
- Use NestJS built-in exception filters
- Implement global exception handling in [src/common/process-exception.handler.ts](mdc:src/common/process-exception.handler.ts)
- Use appropriate HTTP status codes
- Provide meaningful error messages

### Exception Examples
```typescript
throw new BadRequestException('Invalid route parameters');
throw new NotFoundException('Route not found');
throw new InternalServerErrorException('External API failed');
```

## Logging Standards
- Use Winston logger configured in [src/setup/setup.winston.ts](mdc:src/setup/setup.winston.ts)
- Log levels: error, warn, info, debug
- Include request context in logs
- Avoid logging sensitive information

### Logging Examples
```typescript
this.logger.log('Route calculation started', { requestId, destinationCount });
this.logger.error('API call failed', { error: error.message, endpoint });
this.logger.warn('Using fallback route calculation', { reason });
```

## API Documentation
- Use Swagger decorators for all endpoints
- Document request/response DTOs
- Include example values
- Group related endpoints with `@ApiTags()`

### Swagger Examples
```typescript
@ApiProperty({
  description: 'Destination coordinates',
  example: [{ nid: "1", dx: -74.006, dy: 40.7128 }]
})
destpos: RequestCoordinate[];
```

## Testing Standards
- Write unit tests for services
- Use integration tests for controllers
- Mock external dependencies
- Maintain >80% code coverage

### Test Structure
```typescript
describe('FeatureService', () => {
  let service: FeatureService;
  let mockApiService: jest.Mocked<ApiService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        FeatureService,
        { provide: ApiService, useValue: mockApiService }
      ]
    }).compile();

    service = module.get<FeatureService>(FeatureService);
  });
});
```

## Code Organization
- Keep files under 500 lines
- Use barrel exports (index.ts) for modules
- Group related functionality in directories
- Separate concerns (service, controller, dto, types)

## Performance Guidelines
- Use caching for expensive operations
- Implement proper pagination for large datasets
- Use async/await consistently
- Avoid blocking operations in request handlers

## Security Considerations
- Validate all input data with DTOs
- Use class-validator decorators
- Sanitize user inputs
- Implement rate limiting for public endpoints

## Configuration Management
- Use environment variables for configuration
- Access config through ConfigService
- Validate configuration at startup
- Provide default values where appropriate

### Configuration Example
```typescript
const apiUrl = this.configService.get<string>('API_BASE_URL', 'http://localhost:3000');
const timeout = this.configService.get<number>('API_TIMEOUT', 5000);
```
