---
description: 
globs: 
alwaysApply: true
---
# API Integration Guidelines

## Overview
The LBS Proxy service integrates with multiple external APIs for route calculation, mapping, and location services. All external API calls are managed through centralized service layers.

## Key Integration Files
- **API Service**: [src/api/route.api.service.ts](mdc:src/api/route.api.service.ts) - External route API integration
- **API Module**: [src/api/api.module.ts](mdc:src/api/api.module.ts) - API service configuration
- **Type Definitions**: [src/types/route.dto.ts](mdc:src/types/route.dto.ts) - API request/response types

## API Service Pattern
All external API integrations should follow this standardized pattern:

### Base API Service Structure
```typescript
@Injectable()
export class ApiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly logger: Logger
  ) {}

  async callApi<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    params?: any,
    data?: any,
    retries: number = 3
  ): Promise<{ data: T; status: number }> {
    // Implementation with error handling and retries
  }
}
```

## HTTP Configuration
- Use axios through NestJS HttpModule
- Implement timeout handling (default: 30 seconds)
- Configure retry logic for transient failures
- Add request/response interceptors for logging

### HTTP Client Setup
```typescript
HttpModule.register({
  timeout: 30000,
  maxRedirects: 5,
  headers: {
    'Content-Type': 'application/json',
    'User-Agent': 'LBS-Proxy/1.0'
  }
})
```

## Error Handling Strategy

### API Error Types
1. **Network Errors**: Connection timeouts, DNS failures
2. **HTTP Errors**: 4xx client errors, 5xx server errors  
3. **Validation Errors**: Invalid request/response format
4. **Business Logic Errors**: API-specific error responses

### Error Handling Implementation
```typescript
try {
  const response = await this.apiService.callApi('/endpoint', 'POST', null, data);
  return response.data;
} catch (error) {
  if (error.response?.status === 404) {
    throw new NotFoundException('Resource not found');
  } else if (error.response?.status >= 500) {
    throw new ServiceUnavailableException('External service unavailable');
  } else {
    throw new BadRequestException('Invalid request parameters');
  }
}
```

## Retry and Circuit Breaker
- Implement exponential backoff for retries
- Maximum 3 retry attempts for transient failures
- Use circuit breaker pattern for high-failure services
- Log all retry attempts and failures

### Retry Logic Example
```typescript
async callApiWithRetry<T>(endpoint: string, data: any, maxRetries = 3): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await this.callApi(endpoint, data);
    } catch (error) {
      if (attempt === maxRetries || !this.isRetryableError(error)) {
        throw error;
      }
      await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
    }
  }
}
```

## Request/Response Validation
- Validate all outgoing requests against expected schema
- Validate incoming responses for required fields
- Use DTOs for type safety and documentation

### DTO Validation Example
```typescript
export class TmsRouteRequestDto {
  @IsNotEmpty()
  @ApiProperty({ description: 'Starting coordinates' })
  start: Coordinate;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequestCoordinate)
  @ApiProperty({ description: 'Destination coordinates' })
  destpos: RequestCoordinate[];

  @IsOptional()
  @IsString()
  @ApiProperty({ description: 'Request identifier' })
  reqid?: string;
}
```

## Caching Strategy
- Cache frequent API responses to reduce external calls
- Use TTL-based caching for route calculations
- Implement cache invalidation for time-sensitive data
- Consider response size when caching

### Caching Implementation
```typescript
@Injectable()
export class RouteService {
  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async getRoute(params: RouteParams): Promise<TmsRouteResponseDto> {
    const cacheKey = `route:${JSON.stringify(params)}`;
    
    let result = await this.cacheManager.get<TmsRouteResponseDto>(cacheKey);
    if (!result) {
      result = await this.apiService.calculateRoute(params);
      await this.cacheManager.set(cacheKey, result, 300); // 5 minutes TTL
    }
    
    return result;
  }
}
```

## Monitoring and Logging
- Log all external API calls with request/response details
- Monitor API response times and success rates
- Track API usage and quota consumption
- Alert on API failures and degraded performance

### Logging Best Practices
```typescript
this.logger.log('External API call initiated', {
  endpoint,
  method,
  requestId: context.requestId,
  timestamp: new Date().toISOString()
});

this.logger.error('External API call failed', {
  endpoint,
  error: error.message,
  statusCode: error.response?.status,
  requestId: context.requestId
});
```

## Configuration Management
- Store API endpoints and credentials in environment variables
- Use ConfigService for accessing configuration
- Implement configuration validation at startup
- Support different configurations per environment

### Configuration Example
```typescript
// Environment variables
API_BASE_URL=https://api.example.com
API_KEY=your-api-key
API_TIMEOUT=30000
API_RETRY_ATTEMPTS=3

// Service configuration
const config = {
  baseUrl: this.configService.get<string>('API_BASE_URL'),
  apiKey: this.configService.get<string>('API_KEY'),
  timeout: this.configService.get<number>('API_TIMEOUT', 30000),
  retries: this.configService.get<number>('API_RETRY_ATTEMPTS', 3)
};
```

## Rate Limiting
- Implement client-side rate limiting to respect API quotas
- Use token bucket or sliding window algorithms
- Queue requests when rate limits are approached
- Provide backpressure to upstream callers when necessary

## Security Considerations
- Store API keys securely using environment variables
- Use HTTPS for all external API communications
- Implement request signing where required
- Validate and sanitize all API responses
- Never log sensitive information (API keys, personal data)
