---
description: 
globs: 
alwaysApply: true
---
# LBS Proxy Project Overview

## Project Description
This is a Location-Based Services (LBS) proxy service built with NestJS, designed for North America region operations. The service provides route optimization, dispatch management, and mapping capabilities.

## Technology Stack
- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **API Documentation**: Swagger/OpenAPI
- **Caching**: @nestjs/cache-manager
- **Logging**: <PERSON> with daily rotate
- **Configuration**: @nestjs/config
- **Deployment**: Docker with Jenkins CI/CD

## Entry Points
- **Main Application**: [src/main.ts](mdc:src/main.ts) - Bootstrap configuration and server setup
- **Root Module**: [src/app.module.ts](mdc:src/app.module.ts) - Main application module with all feature imports
- **Package Configuration**: [package.json](mdc:package.json) - Dependencies and scripts
- **API Documentation**: [swagger.yaml](mdc:swagger.yaml) - Auto-generated OpenAPI specification

## Core Modules
1. **Route Module**: Route optimization and TMS integration
2. **Search Module**: Location search capabilities  
3. **Dispatch Module**: Dispatch management
4. **Meter Module**: Metering and measurement services
5. **Map Module**: Mapping services
6. **API Module**: External API integrations
7. **Common Module**: Shared utilities and configurations

## Development Guidelines
- Use TypeScript strict mode
- Follow NestJS module structure and dependency injection
- Implement proper error handling with global interceptors
- Use Swagger decorators for API documentation
- Apply caching strategies for performance optimization
- Implement proper logging for debugging and monitoring

## Build and Deployment
- **Development**: `npm run start:dev`
- **Production Build**: `npm run build` 
- **Docker**: Built using [Dockerfile](mdc:Dockerfile)
- **CI/CD**: [Jenkinsfile](mdc:Jenkinsfile) for automated deployment
