---
description: 
globs: 
alwaysApply: false
---
# Testing Guidelines

## Overview
LBS Proxy 프로젝트는 실제 서버에 대한 직접 테스트를 통해 통합 테스트를 수행합니다. Mock을 사용하지 않고 `npm run start:dev`로 실행된 서버(localhost:15001)에 직접 HTTP 요청을 보내는 방식으로 테스트합니다.

## 사전 준비 조건

### 1. 서버 실행
테스트를 실행하기 전에 개발 서버가 실행되어 있어야 합니다:

```bash
npm run start:dev
# 서버가 localhost:15001에서 실행됨을 확인
```

### 2. 서버 상태 확인
```bash
curl http://localhost:15001/health
# 또는 브라우저에서 http://localhost:15001/swagger 확인
```

## Test Structure
```
test/
├── integration/            # 통합 테스트 (실제 서버 호출)
│   ├── route-comparison.integration.spec.ts   # 라우트 API 비교 테스트
│   ├── route.integration.spec.ts              # 라우트 API 통합 테스트
│   ├── search.integration.spec.ts             # 검색 API 통합 테스트
│   ├── dispatch.integration.spec.ts           # 디스패치 API 통합 테스트
│   ├── health.integration.spec.ts             # 헬스체크 통합 테스트
│   └── comparison-results/                    # 비교 테스트 결과 저장
├── fixtures/               # 테스트 데이터
│   ├── route-requests/     # 라우트 요청 데이터
│   └── expected-responses/ # 예상 응답 데이터
└── utils/                  # 테스트 유틸리티
    ├── api-client.ts       # API 클라이언트
    └── test-helpers.ts     # 테스트 헬퍼 함수
```

## Test Configuration Files
- **Jest Config**: [jest.config.js](mdc:jest.config.js) - Jest 설정
- **Test Environment**: 실제 서버 (localhost:15001)
- **HTTP Client**: axios를 사용한 직접 API 호출

## Testing Scripts
```bash
# 통합 테스트 명령어
npm run test:integration          # 통합 테스트 실행
npm run test:integration:watch    # 통합 테스트 Watch 모드
npm run test:integration:debug    # 통합 테스트 디버그 모드

# 특정 모듈별 테스트
npm run test:route               # 라우트 API 테스트
npm run test:search              # 검색 API 테스트
npm run test:dispatch            # 디스패치 API 테스트
```

## API Client Setup

### 공통 API 클라이언트
```typescript
// test/utils/api-client.ts
import axios, { AxiosInstance, AxiosResponse } from 'axios';

export class TestApiClient {
  private client: AxiosInstance;
  
  constructor() {
    this.client = axios.create({
      baseURL: 'http://localhost:15001',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 요청/응답 로깅을 위한 인터셉터
    this.client.interceptors.request.use((config) => {
      console.log(`🚀 API 요청: ${config.method?.toUpperCase()} ${config.url}`);
      if (config.data) {
        console.log('요청 데이터:', JSON.stringify(config.data, null, 2));
      }
      return config;
    });

    this.client.interceptors.response.use(
      (response) => {
        console.log(`✅ API 응답: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        console.error(`❌ API 에러: ${error.response?.status} ${error.message}`);
        throw error;
      }
    );
  }

  // 라우트 API 호출
  async tmsRoute(data: any): Promise<AxiosResponse> {
    return this.client.post('/route/tmsroute', data);
  }

  async tmsRouteList(data: any): Promise<AxiosResponse> {
    return this.client.post('/route/tmsroutelist', data);
  }

  async calculateRoute(data: any): Promise<AxiosResponse> {
    return this.client.post('/route/calculateroute', data);
  }

  async getDriveRange(data: any): Promise<AxiosResponse> {
    return this.client.post('/route/getdriverange', data);
  }

  // 검색 API 호출
  async searchAddress(query: string): Promise<AxiosResponse> {
    return this.client.get(`/search/address?q=${encodeURIComponent(query)}`);
  }

  // 디스패치 API 호출
  async getDispatchInfo(data: any): Promise<AxiosResponse> {
    return this.client.post('/dispatch/info', data);
  }

  // 헬스체크
  async healthCheck(): Promise<AxiosResponse> {
    return this.client.get('/health');
  }

  // 서버 버전 확인
  async getVersion(): Promise<AxiosResponse> {
    return this.client.get('/version');
  }
}
```

## Integration Testing Standards

### 라우트 API 테스트 패턴
```typescript
// test/integration/route.integration.spec.ts
import { TestApiClient } from '../utils/api-client';
import { routeTestData } from '../fixtures/route-requests/basic-route.json';

describe('Route API Integration Tests', () => {
  let apiClient: TestApiClient;

  beforeAll(() => {
    apiClient = new TestApiClient();
  });

  beforeEach(async () => {
    // 서버 상태 확인
    const healthResponse = await apiClient.healthCheck();
    expect(healthResponse.status).toBe(200);
  });

  describe('TMS Route API', () => {
    it('기본 라우트 계산이 성공해야 함', async () => {
      // Arrange
      const requestData = {
        command: 'RequestRoute',
        start: { dx: 127.04498, dy: 37.54253 },
        reqid: `test-${Date.now()}`,
        routeoption: 512,
        destpos: [
          { dx: 127.045, dy: 37.543, nid: 1, postalCode: '05029' },
          { dx: 127.055, dy: 37.553, nid: 2, postalCode: '06234' }
        ]
      };

      // Act
      const response = await apiClient.tmsRoute(requestData);

      // Assert
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.command).toBe('ResponseRoute');
      expect(response.data.order).toBeDefined();
      expect(Array.isArray(response.data.order)).toBe(true);
      expect(response.data.order.length).toBeGreaterThan(0);

      // 응답 데이터 구조 검증
      response.data.order.forEach((item: any) => {
        expect(item).toHaveProperty('nid');
        expect(item).toHaveProperty('visit');
        expect(item).toHaveProperty('estimate');
        expect(item).toHaveProperty('dist');
      });

      console.log('✅ TMS Route 응답:', JSON.stringify(response.data, null, 2));
    });

    it('대량 목적지 라우트 계산이 성공해야 함', async () => {
      // 50개 이상의 목적지 생성
      const destinations = Array.from({ length: 55 }, (_, i) => ({
        dx: 127.0276 + i * 0.001,
        dy: 37.4979 + i * 0.001,
        nid: i + 1,
        postalCode: i < 20 ? '06234' : i < 40 ? '06621' : '05551'
      }));

      const requestData = {
        command: 'RequestRoute',
        start: { dx: 127.04498, dy: 37.54253 },
        reqid: `large-test-${Date.now()}`,
        routeoption: 512,
        destpos: destinations
      };

      const startTime = Date.now();
      const response = await apiClient.tmsRoute(requestData);
      const duration = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.data.order).toBeDefined();
      expect(response.data.order.length).toBe(55);
      expect(duration).toBeLessThan(30000); // 30초 이내

      console.log(`✅ 대량 목적지 처리 시간: ${duration}ms`);
    });

    it('잘못된 요청 데이터에 대한 에러 처리 확인', async () => {
      const invalidRequestData = {
        command: 'RequestRoute',
        // start 필드 누락
        reqid: `error-test-${Date.now()}`,
        destpos: []
      };

      try {
        await apiClient.tmsRoute(invalidRequestData);
        fail('잘못된 요청에 대해 에러가 발생해야 함');
      } catch (error: any) {
        expect(error.response.status).toBeGreaterThanOrEqual(400);
        expect(error.response.status).toBeLessThan(500);
        console.log('✅ 에러 응답:', error.response.data);
      }
    });
  });

  describe('Route List API', () => {
    it('라우트 리스트 계산이 성공해야 함', async () => {
      const requestData = {
        command: 'RequestRouteList',
        reqid: `list-test-${Date.now()}`,
        routelist: [
          {
            start: { dx: 127.04498, dy: 37.54253 },
            destpos: [
              { dx: 127.045, dy: 37.543, nid: 1 },
              { dx: 127.055, dy: 37.553, nid: 2 }
            ]
          }
        ]
      };

      const response = await apiClient.tmsRouteList(requestData);

      expect(response.status).toBe(200);
      expect(response.data.routelist).toBeDefined();
      expect(Array.isArray(response.data.routelist)).toBe(true);
    });
  });
});
```

### 성능 테스트 패턴
```typescript
describe('Performance Tests', () => {
  let apiClient: TestApiClient;

  beforeAll(() => {
    apiClient = new TestApiClient();
  });

  it('동시 요청 처리 성능 테스트', async () => {
    const requestData = {
      command: 'RequestRoute',
      start: { dx: 127.04498, dy: 37.54253 },
      reqid: `concurrent-test-${Date.now()}`,
      routeoption: 512,
      destpos: [
        { dx: 127.045, dy: 37.543, nid: 1, postalCode: '05029' },
        { dx: 127.055, dy: 37.553, nid: 2, postalCode: '06234' }
      ]
    };

    // 10개의 동시 요청 생성
    const requests = Array(10).fill(null).map((_, index) => 
      apiClient.tmsRoute({
        ...requestData,
        reqid: `concurrent-test-${Date.now()}-${index}`
      })
    );

    const startTime = Date.now();
    const results = await Promise.all(requests);
    const duration = Date.now() - startTime;

    // 모든 요청이 성공했는지 확인
    results.forEach((result, index) => {
      expect(result.status).toBe(200);
      expect(result.data.command).toBe('ResponseRoute');
      console.log(`요청 ${index + 1} 완료`);
    });

    expect(duration).toBeLessThan(10000); // 10초 이내
    console.log(`✅ 동시 요청 10개 처리 시간: ${duration}ms`);
  });

  it('응답 시간 측정 테스트', async () => {
    const requestData = {
      command: 'RequestRoute',
      start: { dx: 127.04498, dy: 37.54253 },
      reqid: `timing-test-${Date.now()}`,
      routeoption: 512,
      destpos: Array.from({ length: 20 }, (_, i) => ({
        dx: 127.045 + i * 0.001,
        dy: 37.543 + i * 0.001,
        nid: i + 1,
        postalCode: '05029'
      }))
    };

    const startTime = Date.now();
    const response = await apiClient.tmsRoute(requestData);
    const duration = Date.now() - startTime;

    expect(response.status).toBe(200);
    expect(duration).toBeLessThan(5000); // 5초 이내

    console.log(`✅ 20개 목적지 처리 시간: ${duration}ms`);
    console.log(`평균 목적지당 처리 시간: ${(duration / 20).toFixed(1)}ms`);
  });
});
```

## 테스트 데이터 관리

### 고정 테스트 데이터
```typescript
// test/fixtures/route-requests/basic-route.json
{
  "command": "RequestRoute",
  "start": { "dx": 127.04498, "dy": 37.54253 },
  "reqid": "test-basic-route",
  "routeoption": 512,
  "destpos": [
    { "dx": 127.045, "dy": 37.543, "nid": 1, "postalCode": "05029" },
    { "dx": 127.055, "dy": 37.553, "nid": 2, "postalCode": "06234" },
    { "dx": 127.065, "dy": 37.563, "nid": 3, "postalCode": "06234" }
  ]
}
```

### 동적 테스트 데이터 생성
```typescript
// test/utils/test-helpers.ts
export class TestDataGenerator {
  static generateRouteRequest(destinationCount: number = 5) {
    return {
      command: 'RequestRoute',
      start: { dx: 127.04498, dy: 37.54253 },
      reqid: `generated-test-${Date.now()}`,
      routeoption: 512,
      destpos: Array.from({ length: destinationCount }, (_, i) => ({
        dx: 127.045 + i * 0.001,
        dy: 37.543 + i * 0.001,
        nid: i + 1,
        postalCode: i % 3 === 0 ? '05029' : i % 3 === 1 ? '06234' : '06621'
      }))
    };
  }

  static generateLargeRouteRequest(destinationCount: number = 100) {
    const postalCodes = ['05029', '06234', '06621', '05551', '04524'];
    
    return {
      command: 'RequestRoute',
      start: { dx: 127.04498, dy: 37.54253 },
      reqid: `large-generated-test-${Date.now()}`,
      routeoption: 512,
      destpos: Array.from({ length: destinationCount }, (_, i) => ({
        dx: 127.0276 + (i * 0.001),
        dy: 37.4979 + (i * 0.001),
        nid: i + 1,
        postalCode: postalCodes[i % postalCodes.length]
      }))
    };
  }
}
```

## 실행 환경 검증

### 서버 연결 테스트
```typescript
// test/integration/health.integration.spec.ts
describe('Server Health Check', () => {
  let apiClient: TestApiClient;

  beforeAll(() => {
    apiClient = new TestApiClient();
  });

  it('서버가 정상적으로 실행되고 있어야 함', async () => {
    const response = await apiClient.healthCheck();
    
    expect(response.status).toBe(200);
    console.log('✅ 서버 상태: 정상');
  });

  it('API 버전 정보를 조회할 수 있어야 함', async () => {
    try {
      const response = await apiClient.getVersion();
      expect(response.status).toBe(200);
      console.log('✅ API 버전:', response.data);
    } catch (error) {
      console.log('ℹ️ 버전 API가 구현되지 않았거나 접근할 수 없습니다.');
    }
  });
});
```

## 에러 시나리오 테스트

### 네트워크 에러 처리
```typescript
describe('Error Handling Tests', () => {
  let apiClient: TestApiClient;

  beforeAll(() => {
    apiClient = new TestApiClient();
  });

  it('잘못된 엔드포인트 호출 시 404 에러 반환', async () => {
    try {
      await axios.get('http://localhost:15001/nonexistent-endpoint');
      fail('존재하지 않는 엔드포인트에서 에러가 발생해야 함');
    } catch (error: any) {
      expect(error.response.status).toBe(404);
      console.log('✅ 404 에러 정상 처리');
    }
  });

  it('잘못된 JSON 형식 전송 시 에러 처리', async () => {
    try {
      await axios.post('http://localhost:15001/route/tmsroute', 'invalid-json', {
        headers: { 'Content-Type': 'application/json' }
      });
      fail('잘못된 JSON에서 에러가 발생해야 함');
    } catch (error: any) {
      expect(error.response.status).toBeGreaterThanOrEqual(400);
      console.log('✅ JSON 파싱 에러 정상 처리');
    }
  });
});
```

## 테스트 실행 가이드

### 1. 개발 서버 실행
```bash
# 터미널 1에서 서버 실행
npm run start:dev

# 서버 실행 확인 (다른 터미널에서)
curl http://localhost:15001/health
```

### 2. 통합 테스트 실행
```bash
# 모든 통합 테스트 실행
npm run test:integration

# 특정 테스트 파일만 실행
npm run test:integration -- route.integration.spec.ts

# 특정 테스트 케이스만 실행
npm run test:integration -- --testNamePattern="기본 라우트 계산"
```

### 3. 테스트 결과 분석
```bash
# 상세한 출력과 함께 테스트 실행
npm run test:integration -- --verbose

# 실패한 테스트만 재실행
npm run test:integration -- --onlyFailures
```

## Best Practices

1. **서버 상태 확인**: 각 테스트 시작 전 헬스체크 수행
2. **독립적인 테스트**: 각 테스트는 서로 영향을 주지 않도록 작성
3. **실제 데이터 사용**: Mock 대신 실제 좌표와 주소 데이터 사용
4. **응답 시간 모니터링**: 모든 API 호출의 응답 시간 측정
5. **에러 시나리오 포함**: 정상 케이스뿐만 아니라 에러 케이스도 테스트
6. **로깅 활용**: 요청/응답 데이터를 상세히 로깅하여 디버깅 지원
7. **타임아웃 설정**: 모든 API 호출에 적절한 타임아웃 설정
8. **환경 검증**: 테스트 실행 전 서버 실행 상태 검증

## 주의사항

- **서버 실행 필수**: 테스트 실행 전 반드시 `npm run start:dev`로 서버 실행
- **포트 충돌 확인**: localhost:15001 포트가 사용 가능한지 확인
- **네트워크 연결**: 외부 API 의존성이 있는 경우 네트워크 연결 상태 확인
- **데이터 일관성**: 테스트 데이터가 실제 서비스 로직과 일치하는지 확인
- **리소스 정리**: 장시간 실행되는 테스트 후 리소스 정리 수행
