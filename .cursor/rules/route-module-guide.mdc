---
description: 
globs: 
alwaysApply: true
---
# Route Module Guide

## Overview
The Route module handles TMS (Transportation Management System) route calculations and optimizations. It provides simplified direct API routing after recent architecture changes.

## Key Files
- **Main Service**: [src/route/route.service.ts](mdc:src/route/route.service.ts) - Core route calculation logic
- **Optimizer Service**: [src/route/route-optimizer.service.ts](mdc:src/route/route-optimizer.service.ts) - Route optimization algorithms
- **Route Module**: [src/route/route.module.ts](mdc:src/route/route.module.ts) - Module configuration
- **Type Definitions**: [src/types/route.dto.ts](mdc:src/types/route.dto.ts) - Route-related DTOs and interfaces

## Architecture Changes
The Route service has been simplified from a complex optimization system to direct API calls:

### Current Implementation (Simplified)
```typescript
tmsRoute(body: TmsRouteRequestDto) {
  return this.routeApiService.callApi<TmsRouteResponseDto>('/tmsroute', 'POST', undefined, body, 1);
}
```

### Previous Implementation (Complex Optimization)
- Multi-stage optimization with postal code grouping
- Regrouping logic for 60+ destinations
- Distance-based sorting and batch processing
- Multiple API calls with result merging

## Key Components

### RouteService
- **Purpose**: Main entry point for route calculations
- **Key Methods**:
  - `tmsRoute()`: Primary route calculation method
  - `tmsRouteList()`: Batch route processing
  - `calculateRoute()`: Direct route calculation
  - `getDriveRange()`: Drive range analysis

### RouteOptimizerService
- **Purpose**: Contains optimization algorithms (currently simplified)
- **Key Methods**:
  - `groupDestinationsByPostalCode()`: Groups destinations by postal code
  - `sortGroupsByDistance()`: Sorts groups by distance from start point
  - `calculateDistance()`: Haversine formula for distance calculation
  - `mergeGroupResults()`: Combines multiple route results

## Data Flow
1. **Input**: `TmsRouteRequestDto` with start point and destinations
2. **Processing**: Direct API call to external TMS service
3. **Output**: `TmsRouteResponseDto` with optimized route order and points

## Usage Patterns
```typescript
// Basic route calculation
const routeResult = await routeService.tmsRoute({
  start: { dx: longitude, dy: latitude },
  destpos: [
    { nid: "1", dx: lng1, dy: lat1, postalCode: "12345" },
    { nid: "2", dx: lng2, dy: lat2, postalCode: "12346" }
  ],
  reqid: "unique-request-id"
});
```

## Performance Considerations
- Current implementation prioritizes simplicity over optimization
- Direct API calls may have limitations for large destination sets (>50)
- Consider implementing batch processing for future scalability needs

## Future Enhancement Areas
1. **Reintroduce Optimization**: For large destination sets
2. **Caching Strategy**: Cache frequent route calculations
3. **Parallel Processing**: Handle multiple route requests concurrently
4. **Error Recovery**: Implement fallback mechanisms for API failures
