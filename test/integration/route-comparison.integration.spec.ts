import { TestApiClient } from '../utils/api-client';
import { TestHelpers } from '../utils/test-helpers';
import * as path from 'path';

describe('Route API Comparison Integration Tests', () => {
  let apiClient: TestApiClient;
  const resultsDir = path.join(__dirname, '../results');

  // 테스트할 요청 파일들
  const requestFiles = ['test/request1.json', 'test/request2.json', 'test/request3.json'];

  beforeAll(async () => {
    apiClient = new TestApiClient();
    TestHelpers.ensureResultsDirectory(resultsDir);
    TestHelpers.clearResultsDirectory(resultsDir);

    // 서버 상태 확인 및 대기
    let retries = 10;
    while (retries > 0) {
      try {
        const healthResponse = await apiClient.healthCheck();
        if (healthResponse.status === 200) {
          console.log('✅ 서버 연결 성공');
          break;
        }
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw new Error('❌ 서버에 연결할 수 없습니다. "npm run start:dev"로 서버를 먼저 실행해주세요.');
        }
        console.log(`⏳ 서버 연결 대기 중... (${retries}회 남음)`);
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }
  }, 30000);

  beforeEach(async () => {
    // 서버 상태 확인
    try {
      const healthResponse = await apiClient.healthCheck();
      expect(healthResponse.status).toBe(200);
    } catch (error) {
      throw new Error('❌ 서버 연결이 끊어졌습니다.');
    }
  });

  describe('TMS Route API 비교 테스트', () => {
    requestFiles.forEach((requestFile) => {
      const baseFileName = TestHelpers.getBaseFileName(requestFile);

      it(`${baseFileName} - tmsroute-old vs tmsroute API 비교`, async () => {
        console.log(`\n🧪 테스트 시작: ${baseFileName}`);

        // 1. 요청 데이터 로드
        const requestData = TestHelpers.readJsonFile(requestFile);
        console.log(`📄 요청 파일 로드: ${requestFile}`);
        console.log(`📊 배송지 개수: ${requestData.destpos?.length || 0}개`);

        let oldApiResult: any;
        let newApiResult: any;
        let oldApiDuration: number;
        let newApiDuration: number;

        try {
          // 1. tmsroute API 호출
          console.log('\n🔄 tmsroute API 호출 중...');
          const newApiMeasurement = await TestHelpers.measureExecutionTime(async () => {
            const response = await apiClient.tmsRoute(requestData);
            return response.data;
          });
          newApiResult = newApiMeasurement.result;
          newApiDuration = newApiMeasurement.duration;
          console.log(`⏱️ tmsroute 응답 시간: ${newApiDuration}ms`);

          const newResultPath = path.join(resultsDir, `${baseFileName}-response.json`);

          const newResultWithMeta = {
            ...newApiResult,
            metadata: {
              api: 'tmsroute',
              requestFile: requestFile,
              responseTime: newApiDuration,
              timestamp: new Date().toISOString(),
              destinationCount: requestData.destpos?.length || 0,
            },
          };

          TestHelpers.writeJsonFile(newResultPath, newResultWithMeta);

          expect(newApiResult).toBeDefined();
          expect(newApiResult.command).toBeDefined();

          // 2. tmsroute-old API 호출
          console.log('\n🔄 tmsroute-old API 호출 중...');
          const oldApiMeasurement = await TestHelpers.measureExecutionTime(async () => {
            const response = await apiClient.tmsRouteOld(requestData);
            return response.data;
          });
          oldApiResult = oldApiMeasurement.result;
          oldApiDuration = oldApiMeasurement.duration;
          console.log(`⏱️ tmsroute-old 응답 시간: ${oldApiDuration}ms`);

          // 3. 응답 검증
          expect(oldApiResult).toBeDefined();
          expect(oldApiResult.command).toBeDefined();

          // 5. 결과 저장
          const oldResultPath = path.join(resultsDir, `${baseFileName}-response-old.json`);

          // 응답 시간 메타데이터 추가
          const oldResultWithMeta = {
            ...oldApiResult,
            metadata: {
              api: 'tmsroute-old',
              requestFile: requestFile,
              responseTime: oldApiDuration,
              timestamp: new Date().toISOString(),
              destinationCount: requestData.destpos?.length || 0,
            },
          };

          TestHelpers.writeJsonFile(oldResultPath, oldResultWithMeta);

          // 6. 결과 비교 분석
          const comparison = TestHelpers.compareResponses(oldApiResult, newApiResult);
          console.log('\n📊 API 응답 비교 결과:');
          console.log(`동일 여부: ${comparison.identical ? '✅ 동일' : '❌ 차이 있음'}`);

          if (!comparison.identical) {
            console.log('🔍 차이점:');
            comparison.differences.forEach((diff, index) => {
              console.log(`  ${index + 1}. ${diff}`);
            });
          }

          // 7. 성능 비교
          const performanceRatio = newApiDuration / oldApiDuration;
          console.log('\n⚡ 성능 비교:');
          console.log(`tmsroute-old: ${oldApiDuration}ms`);
          console.log(`tmsroute: ${newApiDuration}ms`);
          console.log(`성능 비율: ${performanceRatio.toFixed(2)}x ${performanceRatio < 1 ? '(개선됨)' : '(느려짐)'}`);

          // 8. 비교 리포트 저장
          const comparisonReport = {
            testFile: baseFileName,
            requestFile: requestFile,
            destinationCount: requestData.destpos?.length || 0,
            timestamp: new Date().toISOString(),
            performance: {
              oldApiDuration,
              newApiDuration,
              performanceRatio,
              improvement: performanceRatio < 1,
            },
            comparison: {
              identical: comparison.identical,
              differences: comparison.differences,
            },
            results: {
              oldApiOrderCount: oldApiResult.order?.length || 0,
              newApiOrderCount: newApiResult.order?.length || 0,
            },
          };

          const reportPath = path.join(resultsDir, `${baseFileName}-comparison-report.json`);
          TestHelpers.writeJsonFile(reportPath, comparisonReport);

          console.log(`\n✅ ${baseFileName} 테스트 완료\n`);
        } catch (error) {
          console.error(`❌ ${baseFileName} 테스트 실패:`, error);

          // 에러가 발생해도 가능한 결과는 저장
          if (oldApiResult) {
            const oldResultPath = path.join(resultsDir, `${baseFileName}-response-old.json`);
            TestHelpers.writeJsonFile(oldResultPath, oldApiResult);
          }
          if (newApiResult) {
            const newResultPath = path.join(resultsDir, `${baseFileName}-response.json`);
            TestHelpers.writeJsonFile(newResultPath, newApiResult);
          }

          throw error;
        }
      }, 300000); // 300초 타임아웃
    });
  });
});
