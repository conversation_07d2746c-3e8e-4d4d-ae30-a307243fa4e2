import { TestApiClient } from '../utils/api-client';

describe('Route API Integration Tests', () => {
  let apiClient: TestApiClient;

  beforeAll(() => {
    apiClient = new TestApiClient();
  });

  beforeEach(async () => {
    // 서버 상태 확인
    try {
      const healthResponse = await apiClient.healthCheck();
      expect(healthResponse.status).toBe(200);
      console.log('✅ 서버 상태: 정상');
    } catch (error) {
      throw new Error('❌ 서버가 실행되지 않았습니다. npm run start:dev로 서버를 먼저 실행해주세요.');
    }
  });

  describe('서버 기본 기능 테스트', () => {
    it('헬스체크가 정상적으로 동작해야 함', async () => {
      const response = await apiClient.healthCheck();

      expect(response.status).toBe(200);
      console.log('✅ 헬스체크 성공');
    });

    it('버전 정보를 조회할 수 있어야 함', async () => {
      try {
        const response = await apiClient.getVersion();
        expect(response.status).toBe(200);
        console.log('✅ 버전 정보:', response.data);
      } catch (error: any) {
        if (error.response?.status === 404) {
          console.log('ℹ️ 버전 API가 구현되지 않았거나 경로가 다릅니다.');
        } else {
          throw error;
        }
      }
    });
  });

  describe('TMS Route API 기본 테스트', () => {
    const basicRequestData = {
      command: 'RequestRoute',
      start: { dx: 127.04498, dy: 37.54253 },
      reqid: `test-${Date.now()}`,
      routeoption: 512,
      destpos: [
        { dx: 127.045, dy: 37.543, nid: 1, postalCode: '05029' },
        { dx: 127.055, dy: 37.553, nid: 2, postalCode: '06234' },
        { dx: 127.065, dy: 37.563, nid: 3, postalCode: '06234' },
      ],
    };

    it('기본 TMS Route 계산이 성공해야 함', async () => {
      console.log('🧪 기본 TMS Route API 테스트 시작');

      const startTime = Date.now();
      const response = await apiClient.tmsRoute(basicRequestData);
      const duration = Date.now() - startTime;

      // 응답 구조 검증
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.command).toBeDefined();
      expect(response.data.order).toBeDefined();
      expect(Array.isArray(response.data.order)).toBe(true);
      expect(response.data.order.length).toBeGreaterThan(0);

      // 응답 데이터 구조 검증
      response.data.order.forEach((item: any, index: number) => {
        expect(item).toHaveProperty('nid');
        expect(item).toHaveProperty('visit');
        expect(item).toHaveProperty('estimate');
        expect(item).toHaveProperty('dist');
        console.log(`  순서 ${index + 1}: nid=${item.nid}, visit=${item.visit}ms, dist=${item.dist}m`);
      });

      console.log(`✅ TMS Route 응답 시간: ${duration}ms`);
      console.log(`📊 최적화된 순서: ${response.data.order.length}개 지점`);
    });

    it('TMS Route Old 계산이 성공해야 함', async () => {
      console.log('🧪 TMS Route Old API 테스트 시작');

      const startTime = Date.now();
      const response = await apiClient.tmsRouteOld(basicRequestData);
      const duration = Date.now() - startTime;

      // 응답 구조 검증
      expect(response.status).toBe(200);
      expect(response.data).toBeDefined();
      expect(response.data.command).toBeDefined();
      expect(response.data.order).toBeDefined();
      expect(Array.isArray(response.data.order)).toBe(true);
      expect(response.data.order.length).toBeGreaterThan(0);

      console.log(`✅ TMS Route Old 응답 시간: ${duration}ms`);
      console.log(`📊 최적화된 순서: ${response.data.order.length}개 지점`);
    });

    it('대용량 배송지 처리가 성공해야 함', async () => {
      // 20개 배송지 생성
      const destinations = Array.from({ length: 20 }, (_, i) => ({
        dx: 127.0276 + i * 0.001,
        dy: 37.4979 + i * 0.001,
        nid: i + 1,
        postalCode: i < 7 ? '05029' : i < 14 ? '06234' : '06621',
      }));

      const largeRequestData = {
        command: 'RequestRoute',
        start: { dx: 127.04498, dy: 37.54253 },
        reqid: `large-test-${Date.now()}`,
        routeoption: 512,
        destpos: destinations,
      };

      console.log('🧪 대용량 배송지 테스트 시작 (20개 지점)');

      const startTime = Date.now();
      const response = await apiClient.tmsRoute(largeRequestData);
      const duration = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.data.order).toBeDefined();
      expect(response.data.order.length).toBe(20);
      expect(duration).toBeLessThan(30000); // 30초 이내

      console.log(`✅ 대용량 처리 완료: ${duration}ms`);
      console.log(`📊 처리 속도: ${(duration / 20).toFixed(1)}ms per destination`);
    });
  });

  describe('에러 처리 테스트', () => {
    it('잘못된 요청 데이터에 대한 에러 처리 확인', async () => {
      const invalidRequestData = {
        command: 'RequestRoute',
        // start 필드 누락
        reqid: `error-test-${Date.now()}`,
        destpos: [],
      };

      try {
        await apiClient.tmsRoute(invalidRequestData);
        fail('잘못된 요청에 대해 에러가 발생해야 함');
      } catch (error: any) {
        expect(error.response.status).toBeGreaterThanOrEqual(400);
        expect(error.response.status).toBeLessThan(500);
        console.log('✅ 잘못된 요청에 대한 에러 처리 정상:', error.response.status);
      }
    });

    it('빈 배송지 배열에 대한 처리 확인', async () => {
      const emptyDestRequestData = {
        command: 'RequestRoute',
        start: { dx: 127.04498, dy: 37.54253 },
        reqid: `empty-test-${Date.now()}`,
        routeoption: 512,
        destpos: [],
      };

      try {
        const response = await apiClient.tmsRoute(emptyDestRequestData);
        // 빈 배송지에 대해서는 빈 order 배열 또는 에러가 반환될 수 있음
        if (response.status === 200) {
          expect(response.data.order).toBeDefined();
          expect(Array.isArray(response.data.order)).toBe(true);
          console.log('✅ 빈 배송지 처리 성공:', response.data.order.length, '개 결과');
        }
      } catch (error: any) {
        // 에러가 발생하는 것도 정상적인 처리일 수 있음
        expect(error.response.status).toBeGreaterThanOrEqual(400);
        console.log('✅ 빈 배송지에 대한 에러 처리:', error.response.status);
      }
    });
  });

  describe('TMS Route List API 테스트', () => {
    it('라우트 리스트 계산이 성공해야 함', async () => {
      const requestData = {
        command: 'RequestRouteList',
        reqid: `list-test-${Date.now()}`,
        routelist: [
          {
            start: { dx: 127.04498, dy: 37.54253 },
            destpos: [
              { dx: 127.045, dy: 37.543, nid: 1 },
              { dx: 127.055, dy: 37.553, nid: 2 },
            ],
          },
          {
            start: { dx: 127.05498, dy: 37.55253 },
            destpos: [
              { dx: 127.065, dy: 37.563, nid: 3 },
              { dx: 127.075, dy: 37.573, nid: 4 },
            ],
          },
        ],
      };

      console.log('🧪 TMS Route List API 테스트 시작');

      const response = await apiClient.tmsRouteList(requestData);

      expect(response.status).toBe(200);
      expect(response.data.routelist).toBeDefined();
      expect(Array.isArray(response.data.routelist)).toBe(true);
      expect(response.data.routelist.length).toBe(2);

      console.log('✅ TMS Route List 성공:', response.data.routelist.length, '개 라우트');
    });
  });
});
