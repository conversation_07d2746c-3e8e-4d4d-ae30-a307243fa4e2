import { TestApiClient } from '../utils/api-client';

describe('Server Health Check Integration Tests', () => {
  let apiClient: TestApiClient;

  beforeAll(() => {
    apiClient = new TestApiClient();
  });

  describe('서버 연결 및 상태 테스트', () => {
    it('서버가 정상적으로 실행되고 있어야 함', async () => {
      const response = await apiClient.healthCheck();

      expect(response.status).toBe(200);
      console.log('✅ 서버 상태: 정상');
      console.log('📡 서버 URL: http://localhost:15001');
    });

    it('API 버전 정보를 조회할 수 있어야 함', async () => {
      try {
        const response = await apiClient.getVersion();
        expect(response.status).toBe(200);
        console.log('✅ API 버전 정보:', response.data);
      } catch (error: any) {
        if (error.response?.status === 404) {
          console.log('ℹ️ 버전 API가 구현되지 않았거나 접근할 수 없습니다.');
          // 404는 정상적인 상황일 수 있으므로 테스트를 실패시키지 않음
        } else {
          console.log('⚠️ 버전 API 호출 중 예상치 못한 에러:', error.message);
          throw error;
        }
      }
    });

    it('서버 응답 시간이 허용 범위 내에 있어야 함', async () => {
      const startTime = Date.now();
      const response = await apiClient.healthCheck();
      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(5000); // 5초 이내 응답

      console.log(`⏱️ 서버 응답 시간: ${responseTime}ms`);

      if (responseTime > 1000) {
        console.log('⚠️ 서버 응답이 느립니다 (1초 초과)');
      } else {
        console.log('✅ 서버 응답 속도 양호');
      }
    });
  });

  describe('서버 안정성 테스트', () => {
    it('연속 요청에 대해 안정적으로 응답해야 함', async () => {
      const requests = Array(5)
        .fill(null)
        .map(() => apiClient.healthCheck());

      const responses = await Promise.all(requests);

      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
        console.log(`요청 ${index + 1}: ✅ 성공`);
      });

      console.log('✅ 연속 요청 테스트 완료: 5/5 성공');
    });

    it('동시 요청 처리가 가능해야 함', async () => {
      const concurrentRequests = 10;
      console.log(`🚀 동시 요청 ${concurrentRequests}개 테스트 시작`);

      const startTime = Date.now();
      const requests = Array(concurrentRequests)
        .fill(null)
        .map(() => apiClient.healthCheck());

      const responses = await Promise.all(requests);
      const totalTime = Date.now() - startTime;

      // 모든 요청이 성공했는지 확인
      responses.forEach((response, index) => {
        expect(response.status).toBe(200);
      });

      const avgResponseTime = totalTime / concurrentRequests;
      console.log(`✅ 동시 요청 ${concurrentRequests}개 모두 성공`);
      console.log(`⏱️ 총 처리 시간: ${totalTime}ms`);
      console.log(`📊 평균 응답 시간: ${avgResponseTime.toFixed(1)}ms`);

      // 동시 요청 처리 시간이 순차 처리보다 효율적인지 확인
      expect(totalTime).toBeLessThan(concurrentRequests * 1000); // 각각 1초씩 걸린다면 10초, 동시 처리는 훨씬 빨라야 함
    });
  });
});
