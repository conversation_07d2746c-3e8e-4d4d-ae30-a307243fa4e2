# LBS Proxy 통합 테스트 가이드

## 개요

이 디렉토리는 LBS Proxy 서비스의 통합 테스트를 포함합니다. 실제 서버에 HTTP 요청을 보내는 방식으로 테스트를 진행합니다.

## 사전 준비

### 1. 서버 실행

테스트 실행 전에 반드시 개발 서버를 실행해야 합니다:

```bash
# 프로젝트 루트에서 실행
npm run start:dev
```

서버가 `http://localhost:15001`에서 실행되는지 확인:

```bash
curl http://localhost:15001/health
```

### 2. 의존성 설치

```bash
npm install
```

## 테스트 파일 구조

```
test/
├── integration/                           # 통합 테스트
│   ├── route-comparison.integration.spec.ts   # 라우트 API 비교 테스트 (메인)
│   ├── route.integration.spec.ts              # 기본 라우트 API 테스트
│   └── health.integration.spec.ts             # 헬스체크 테스트
├── utils/                                 # 테스트 유틸리티
│   ├── api-client.ts                      # API 클라이언트
│   └── test-helpers.ts                    # 테스트 헬퍼 함수
├── results/                               # 테스트 결과 저장 (자동 생성)
├── request1.json                          # 테스트 데이터 1 (5개 배송지)
├── request2.json                          # 테스트 데이터 2 (6개 배송지)
├── request3.json                          # 테스트 데이터 3 (400+개 배송지)
└── README.md                              # 이 파일
```

## 테스트 실행

### 1. 라우트 API 비교 테스트 (메인)

request1.json, request2.json, request3.json 파일을 순차적으로 읽어서 `tmsroute-old`와 `tmsroute` API를 비교합니다:

```bash
# 비교 테스트 실행
npm run test:route-api-comparison

# 또는 Jest 직접 실행
npx jest test/integration/route-comparison.integration.spec.ts --verbose
```

### 2. 기본 라우트 API 테스트

```bash
npm run test:integration -- route.integration.spec.ts
```

### 3. 헬스체크 테스트

```bash
npm run test:integration -- health.integration.spec.ts
```

### 4. 모든 통합 테스트 실행

```bash
npm run test:integration
```

## 테스트 결과

### 결과 파일

테스트 실행 후 `test/results/` 디렉토리에 다음 파일들이 생성됩니다:

1. **API 응답 결과**:

   - `request1-response-old.json` - tmsroute-old API 응답
   - `request1-response.json` - tmsroute API 응답
   - `request2-response-old.json` - tmsroute-old API 응답
   - `request2-response.json` - tmsroute API 응답
   - `request3-response-old.json` - tmsroute-old API 응답
   - `request3-response.json` - tmsroute API 응답

2. **비교 리포트**:

   - `request1-comparison-report.json` - 두 API 비교 결과
   - `request2-comparison-report.json` - 두 API 비교 결과
   - `request3-comparison-report.json` - 두 API 비교 결과

3. **성능 분석**:
   - `performance-analysis-report.json` - 종합 성능 분석 리포트

### 결과 분석

각 리포트 파일에는 다음 정보가 포함됩니다:

- **성능 비교**: 응답 시간, 개선율
- **결과 비교**: API 응답의 동일성, 차이점
- **메타데이터**: 타임스탬프, 배송지 개수, 요청 파일 정보

## 테스트 데이터

### request1.json

- 배송지: 5개
- 특징: 기본적인 경로 최적화 테스트

### request2.json

- 배송지: 6개
- 특징: request1과 유사하지만 배송지 1개 추가

### request3.json

- 배송지: 400+개
- 특징: 대용량 배송지 처리 성능 테스트

## 성능 기준

### 응답 시간 기준

- **기본 요청 (5-10개 배송지)**: 5초 이내
- **중간 요청 (20-50개 배송지)**: 30초 이내
- **대용량 요청 (100+개 배송지)**: 60초 이내

### 성공 기준

- 모든 API 호출이 HTTP 200 응답
- 응답 데이터에 필수 필드 포함 (`command`, `order`)
- `order` 배열에 최소 1개 이상의 요소
- 각 order 요소에 `nid`, `visit`, `estimate`, `dist` 필드 포함

## 문제 해결

### 서버 연결 실패

```
❌ 서버가 실행되지 않았습니다. npm run start:dev로 서버를 먼저 실행해주세요.
```

→ `npm run start:dev`로 서버를 먼저 실행하세요.

### 포트 충돌

서버가 15001 포트를 사용할 수 없는 경우:

1. 다른 프로세스가 포트를 사용하는지 확인: `lsof -i :15001`
2. 서버 포트 설정 확인
3. API 클라이언트의 baseURL 수정

### 타임아웃 에러

대용량 요청에서 타임아웃이 발생하는 경우:

1. Jest 타임아웃 설정 확인 (`jest-e2e.json`)
2. API 클라이언트 타임아웃 설정 확인 (`api-client.ts`)
3. 서버의 요청 처리 시간 확인

## 주의사항

1. **실제 서버 필요**: Mock이 아닌 실제 서버에 요청을 보냅니다.
2. **네트워크 의존성**: 외부 API 호출이 포함된 경우 네트워크 상태에 영향을 받을 수 있습니다.
3. **순차 실행**: 대용량 테스트는 서버 부하를 고려하여 순차적으로 실행됩니다.
4. **결과 파일**: 테스트 결과 파일은 덮어쓰기됩니다. 필요시 백업하세요.

## 확장 가능성

### 새로운 테스트 추가

1. `test/integration/` 디렉토리에 새로운 `.spec.ts` 파일 생성
2. `TestApiClient` 클래스에 새로운 API 메서드 추가
3. `TestHelpers` 클래스에 필요한 유틸리티 함수 추가

### 새로운 테스트 데이터 추가

1. `test/` 디렉토리에 새로운 JSON 파일 생성
2. `route-comparison.integration.spec.ts`의 `requestFiles` 배열에 추가

### 커스텀 비교 로직 추가

`TestHelpers.compareResponses()` 메서드를 수정하여 새로운 비교 기준을 추가할 수 있습니다.
