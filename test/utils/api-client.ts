import axios, { AxiosInstance, AxiosResponse } from 'axios';

export class TestApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: 'http://localhost:15001',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 요청/응답 로깅을 위한 인터셉터
    this.client.interceptors.request.use((config) => {
      console.log(`🚀 API 요청: ${config.method?.toUpperCase()} ${config.url}`);
      if (config.data) {
        console.log('요청 데이터:', JSON.stringify(config.data, null, 2));
      }
      return config;
    });

    this.client.interceptors.response.use(
      (response) => {
        console.log(`✅ API 응답: ${response.status} ${response.statusText}`);
        return response;
      },
      (error) => {
        console.error(`❌ API 에러: ${error.response?.status} ${error.message}`);
        throw error;
      },
    );
  }

  // 라우트 API 호출
  async tmsRoute(data: any): Promise<AxiosResponse> {
    return this.client.post('/tmsroute', data);
  }

  async tmsRouteOld(data: any): Promise<AxiosResponse> {
    return this.client.post('/tmsroute-old', data);
  }

  async tmsRouteList(data: any): Promise<AxiosResponse> {
    return this.client.post('/tmsroutelist', data);
  }

  async calculateRoute(data: any): Promise<AxiosResponse> {
    return this.client.post('/calculateroute', data);
  }

  async getDriveRange(data: any): Promise<AxiosResponse> {
    return this.client.post('/getdriverange', data);
  }

  // 헬스체크
  async healthCheck(): Promise<AxiosResponse> {
    return this.client.get('/health');
  }

  // 서버 버전 확인
  async getVersion(): Promise<AxiosResponse> {
    return this.client.get('/route/version');
  }
}
