import * as fs from 'fs';
import * as path from 'path';

export class TestHelpers {
  /**
   * JSON 파일을 읽어서 객체로 반환
   */
  static readJsonFile(filePath: string): any {
    try {
      const fullPath = path.resolve(filePath);
      const fileContent = fs.readFileSync(fullPath, 'utf8');
      return JSON.parse(fileContent);
    } catch (error) {
      throw new Error(`JSON 파일 읽기 실패: ${filePath} - ${error}`);
    }
  }

  /**
   * 객체를 JSON 파일로 저장
   */
  static writeJsonFile(filePath: string, data: any): void {
    try {
      const fullPath = path.resolve(filePath);
      const dir = path.dirname(fullPath);

      // 디렉토리가 없으면 생성
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      const jsonContent = JSON.stringify(data, null, 2);
      fs.writeFileSync(fullPath, jsonContent, 'utf8');
      console.log(`✅ 파일 저장 완료: ${fullPath}`);
    } catch (error) {
      throw new Error(`JSON 파일 저장 실패: ${filePath} - ${error}`);
    }
  }

  /**
   * 테스트 결과 비교를 위한 헬퍼
   */
  static compareResponses(
    response1: any,
    response2: any,
  ): {
    identical: boolean;
    differences: string[];
  } {
    const differences: string[] = [];

    // 기본 응답 구조 비교
    if (response1.command !== response2.command) {
      differences.push(`command 필드 차이: ${response1.command} vs ${response2.command}`);
    }

    // order 배열 비교
    if (response1.order && response2.order) {
      if (response1.order.length !== response2.order.length) {
        differences.push(`order 배열 길이 차이: ${response1.order.length} vs ${response2.order.length}`);
      }

      // 순서 비교 (nid 기준)
      for (let i = 0; i < Math.min(response1.order.length, response2.order.length); i++) {
        if (response1.order[i].nid !== response2.order[i].nid) {
          differences.push(`order[${i}] nid 차이: ${response1.order[i].nid} vs ${response2.order[i].nid}`);
        }
      }
    }

    return {
      identical: differences.length === 0,
      differences,
    };
  }

  /**
   * 응답 시간 측정 헬퍼
   */
  static async measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    const result = await fn();
    const duration = Date.now() - startTime;
    return { result, duration };
  }

  /**
   * 파일 경로에서 기본 이름 추출 (확장자 제거)
   */
  static getBaseFileName(filePath: string): string {
    return path.basename(filePath, path.extname(filePath));
  }

  /**
   * 결과 디렉토리 생성
   */
  static ensureResultsDirectory(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 결과 디렉토리 생성: ${dirPath}`);
    }
  }

  /**
   * 결과 디렉토리의 기존 파일들 삭제
   */
  static clearResultsDirectory(dirPath: string): void {
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      files.forEach((file) => {
        const filePath = path.join(dirPath, file);
        if (fs.statSync(filePath).isFile()) {
          fs.unlinkSync(filePath);
        }
      });
      console.log(`🗑️ 이전 결과 파일 삭제 완료: ${files.length}개 파일`);
    }
  }
}
