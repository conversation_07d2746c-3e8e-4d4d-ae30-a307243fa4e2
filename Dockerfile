# Build stage
FROM node:20-alpine AS build

# 작업 디렉토리 설정
WORKDIR /usr/src/app

# package.json과 package-lock.json 복사
COPY package*.json ./

# 의존성 설치
RUN npm ci

# 소스 코드 복사
COPY . .

# 애플리케이션 빌드
RUN npm run build

# Production stage
FROM node:20-alpine

# bash와 curl 설치
RUN apk add --no-cache bash curl

# 작업 디렉토리 설정
WORKDIR /usr/src/app

# package.json과 package-lock.json 복사
COPY package*.json ./

# 프로덕션 의존성만 설치
RUN npm ci --only=production

# 빌드 스테이지에서 빌드된 에셋 복사
COPY --from=build /usr/src/app/dist ./dist

# NODE_ENV 환경 변수 설정
ENV NODE_ENV production

# 타임존을 KST로 설정
RUN apk add --no-cache tzdata
ENV TZ Asia/Seoul

# 앱이 실행되는 포트 노출
EXPOSE 3003

# 애플리케이션 시작
CMD ["node", "dist/main"]
