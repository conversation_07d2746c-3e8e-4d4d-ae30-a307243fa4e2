pipeline {
    agent any

    environment {
        // Docker 관련 환경 변수
        DOCKER_REGISTRY = 'harbor.logisteq.com'
        DOCKER_CREDENTIALS_ID = 'harbor-login'
        BUILD_TAG = new Date().format('yyyyMMdd-HHmmss')
        GIT_CREDENTIALS_ID = 'gitlab-login'

        // 신규 설정 시 수정해야 하는 환경 변수 (IMAGE_NAME, GIT_REPOSITORY, FILE_PATH)
        IMAGE_NAME = 'lbs-core/lbs-proxy'
        GIT_REPOSITORY = 'gitlab.logisteq.com/infra/lbs-proxy.git'
        GIT_BRANCH = 'main'

        DEPLOY_GIT_REPOSITORY = 'gitlab.logisteq.com/infra/dev-k8s-cfgstore.git'
        DEPLOY_GIT_BRANCH = 'main'
        FILE_PATH = 'basic-helm-chart/lbs-proxy-values.yaml'

        // 환경 변수
        PREFIX_HTTPS = 'https://'
    }

    tools {
        jdk 'jdk-17'
    }

    stages {
        stage('Git Clone') {
            steps {
                script {
                    gitClone()
                }
            }
        }

        stage('Build Image') {
            steps {
                script {
                    buildDockerImage()
                }
            }
        }

        stage('Push Image') {
            steps {
                script {
                    pushDockerImage()
                }
            }
        }

        stage('Update Deploy Repository') {
            steps {
                script {
                    updateDeployRepository()
                }
            }
        }

        stage('Complete') {
            steps {
                script {
                    echo 'The end'
                }
            }
        }
    }
}

// Git 클론 함수
def gitClone() {
    git branch: "${GIT_BRANCH}", credentialsId: "${GIT_CREDENTIALS_ID}", url: "${PREFIX_HTTPS}${GIT_REPOSITORY}"
}

// Docker 이미지 빌드 함수
def buildDockerImage() {
    def customImage = docker.build("${IMAGE_NAME}")
    customImage.tag('latest')
    customImage.tag("${BUILD_TAG}")
}

// Docker 이미지 푸시 함수
def pushDockerImage() {
    docker.withRegistry("${PREFIX_HTTPS}${DOCKER_REGISTRY}", "${DOCKER_CREDENTIALS_ID}") {
        docker.image("${IMAGE_NAME}").push("${BUILD_TAG}")
        docker.image("${IMAGE_NAME}").push('latest')
    }
}

// 배포 저장소 업데이트 함수
def updateDeployRepository() {
    dir('deploy-repo') {
        git branch: "${DEPLOY_GIT_BRANCH}", credentialsId: "${GIT_CREDENTIALS_ID}", url: "${PREFIX_HTTPS}${DEPLOY_GIT_REPOSITORY}"

        withCredentials([usernamePassword(
            credentialsId: "${GIT_CREDENTIALS_ID}",
            usernameVariable: 'GIT_USERNAME',
            passwordVariable: 'GIT_PASSWORD'
        )]) {
            sh """
                git config user.email '<EMAIL>'
                git config user.name 'jenkins'
                git config pull.rebase false

                groovy -e "
                    def file = new File('${FILE_PATH}')
                    def content = file.text
                    def newContent = content.replaceAll(/tag: \".*\"/, 'tag: \"${BUILD_TAG}\"')
                    file.write(newContent)
                "

                git add .
                git commit -m "Update image tag to ${IMAGE_NAME}:${BUILD_TAG}"
                git push ${PREFIX_HTTPS}${GIT_USERNAME}:${GIT_PASSWORD}@${DEPLOY_GIT_REPOSITORY} ${DEPLOY_GIT_BRANCH}
            """
        }
    }
}
