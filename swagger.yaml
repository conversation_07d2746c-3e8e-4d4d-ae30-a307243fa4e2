openapi: 3.0.0
paths:
  /search/version:
    get:
      operationId: SearchController_getVersion
      parameters: []
      responses:
        '200':
          description: ''
      tags: &ref_0
        - Search APIs
  /searchaddress:
    get:
      operationId: SearchController_searchAddress
      summary: 주소검색
      parameters:
        - name: addr
          required: true
          in: query
          description: 주소(지번 주소 및 도로명 주소)를 이용하여 위치의 상세정보 검색을 수행
          example: 서울시 종로구 돈화문로6가길 2
          schema:
            type: string
        - name: xpos
          required: false
          in: query
          description: 검색요청 X 좌표
          example: 127.04491
          schema:
            type: number
        - name: ypos
          required: false
          in: query
          description: 검색요청 Y 좌표
          example: 37.54265
          schema:
            type: number
        - name: sort
          required: false
          in: query
          description: 검색결과 정렬을 위한 설정 값
          example: 0
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchAddressResponseDto'
      tags: *ref_0
  /search:
    get:
      operationId: SearchController_search
      summary: 통합검색
      parameters:
        - name: keyword
          required: true
          in: query
          description: 통합검색을 위한 검색어
          example: 서울숲
          schema:
            type: string
        - name: xpos
          required: false
          in: query
          description: 검색요청 X 좌표
          example: 127.04491
          schema:
            type: number
        - name: ypos
          required: false
          in: query
          description: 검색요청 Y 좌표
          example: 37.54265
          schema:
            type: number
        - name: sort
          required: false
          in: query
          description: 검색결과 정렬을 위한 설정 값
          example: 0
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponseDto'
      tags: *ref_0
  /extsearch:
    get:
      operationId: SearchController_extSearch
      summary: 확장된 통합검색
      parameters:
        - name: keyword
          required: true
          in: query
          description: 통합검색을 위한 검색어
          example: 서울숲
          schema:
            type: string
        - name: xpos
          required: false
          in: query
          description: 검색요청 X 좌표
          example: 127.04491
          schema:
            type: number
        - name: ypos
          required: false
          in: query
          description: 검색요청 Y 좌표
          example: 37.54265
          schema:
            type: number
        - name: sort
          required: false
          in: query
          description: 검색결과 정렬을 위한 설정 값
          example: 0
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchResponseDto'
      tags: *ref_0
  /searchcoord:
    get:
      operationId: SearchController_searchCoord
      summary: 좌표검색
      parameters:
        - name: xpos
          required: true
          in: query
          description: 검색요청 X 좌표
          example: 127.0268
          schema:
            type: number
        - name: ypos
          required: true
          in: query
          description: 검색요청 Y 좌표
          example: 37.61516
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchCoordResponseDto'
      tags: *ref_0
  /extsearchcoord:
    get:
      operationId: SearchController_extSearchCoord
      summary: 확장된 좌표검색
      parameters:
        - name: xpos
          required: true
          in: query
          description: 검색요청 X 좌표
          example: 127.0268
          schema:
            type: number
        - name: ypos
          required: true
          in: query
          description: 검색요청 Y 좌표
          example: 37.61516
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchCoordResponseDto'
      tags: *ref_0
  /searchentrance:
    get:
      operationId: SearchController_searchEntrance
      summary: 입구점검색
      parameters:
        - name: xpos
          required: true
          in: query
          description: 검색요청 X 좌표
          example: 127.0268
          schema:
            type: number
        - name: ypos
          required: true
          in: query
          description: 검색요청 Y 좌표
          example: 37.61516
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchEntranceResponseDto'
      tags: *ref_0
  /geocoding:
    post:
      operationId: SearchController_geocoding
      summary: 지오코딩
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GeocodingRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GeocodingResponseDto'
        '201':
          description: ''
      tags: *ref_0
  /searcharea:
    get:
      operationId: SearchController_searchArea
      summary: 지오펜스
      parameters:
        - name: xpos
          required: true
          in: query
          description: 검색요청 X 좌표
          example: 127.1234937
          schema:
            type: number
        - name: ypos
          required: true
          in: query
          description: 검색요청 Y 좌표
          example: 37.351992
          schema:
            type: number
        - name: expand
          required: false
          in: query
          description: 확장 거리 정보(미터)
          example: 100
          schema:
            type: number
        - name: type
          required: false
          in: query
          description: 영역 검색 종류
          example: 0
          schema:
            type: number
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchAreaResponseDto'
      tags: *ref_0
  /searchpoi:
    get:
      operationId: SearchController_searchPoi
      summary: POI 검색
      description: 건물명이나 상호명 등의 명칭으로 위치 검색을 수행하며, 초성 입력으로도 검색이 가능합니다.
      parameters:
        - name: keyword
          required: true
          in: query
          description: |-
            POI 검색을 위한 검색어
                  - 건물명칭 (예: 서울숲포휴)
                  - 상호명 (예: 엔지스테크널러지)
                  - 초성 (예: ㅅㅇㅅ)
          example: 서울숲
          schema:
            type: string
        - name: xpos
          required: false
          in: query
          description: WGS 84 X좌표(longitude). 검색 기준점으로 사용되며, 거리 계산 및 정렬에 활용됩니다.
          example: 127.04491
          schema:
            type: number
        - name: ypos
          required: false
          in: query
          description: WGS 84 Y좌표(latitude). 검색 기준점으로 사용되며, 거리 계산 및 정렬에 활용됩니다.
          example: 37.54265
          schema:
            type: number
        - name: sort
          required: false
          in: query
          description: |-
            검색결과 정렬 옵션:
                  - 0: 정확도 순 (기본값)
                  - 1: 거리 순 (좌표 입력 시)
                  - 2: 명칭 순
          example: 0
          schema:
            type: number
      responses:
        '200':
          description: 검색 성공
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchPoiResponseDto'
        '204':
          description: 검색 결과 없음
        '400':
          description: 잘못된 요청 파라미터
        '404':
          description: 검색 서버 연결 오류
      tags: *ref_0
  /searchdistcode:
    get:
      operationId: SearchController_searchDistCode
      summary: 행정동코드로 검색
      description: 행정동코드(distcode)와 선택적인 키워드를 사용하여 위치 정보를 검색합니다.
      parameters:
        - name: distcode
          required: true
          in: query
          description: 검색할 행정동코드 (10자리 숫자)
          example: 1100000000
          schema:
            type: number
        - name: keyword
          required: false
          in: query
          description: 선택적 검색 키워드
          example: 카페
          schema:
            type: string
      responses:
        '200':
          description: 검색 성공
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchDistCodeResponseDto'
        '400':
          description: '잘못된 요청 파라미터 (예: distcode 누락 또는 형식 오류)'
        '500':
          description: 서버 내부 오류
      tags: *ref_0
  /route/version:
    get:
      operationId: RouteController_getVersion
      parameters: []
      responses:
        '200':
          description: ''
      tags: &ref_1
        - Route APIs
  /tmsroute:
    post:
      operationId: RouteController_tmsRoute
      summary: route search
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TmsRouteRequestDto'
            examples:
              sample:
                summary: Sample TMS Route Request
                value:
                  command: RequestRoute
                  start:
                    dx: 127.16948
                    dy: 37.53785
                  reqid: '1600658759187'
                  routeoption: 512
                  destpos:
                    - dx: 127.08671
                      dy: 37.53253
                      nid: 1
                  height: 0
                  weight: 0
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteResponseDto'
      tags: *ref_1
  /tmsroute-old:
    post:
      operationId: RouteController_tmsRouteOld
      summary: route search
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TmsRouteRequestDto'
            examples:
              sample:
                summary: Sample TMS Route Request
                value:
                  command: RequestRoute
                  start:
                    dx: 127.16948
                    dy: 37.53785
                  reqid: '1600658759187'
                  routeoption: 512
                  destpos:
                    - dx: 127.08671
                      dy: 37.53253
                      nid: 1
                  height: 0
                  weight: 0
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteResponseDto'
      tags: *ref_1
  /tmsroutelist:
    post:
      operationId: RouteController_tmsRouteList
      summary: calculate multiple routes
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TmsRouteListRequestDto'
            examples:
              sample:
                summary: Sample
                value:
                  command: RequestRouteList
                  routeoption: 512
                  routecount: 2
                  routelist:
                    - start:
                        dx: 127.145057
                        dy: 37.542739
                      destpos:
                        - nid: '10'
                          dx: 127.046047
                          dy: 37.547509
                    - start:
                        dx: 126.992913
                        dy: 37.570967
                      destpos:
                        - nid: '20'
                          dx: 126.89617
                          dy: 37.52172
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteListResponseDto'
        '201':
          description: ''
      tags: *ref_1
  /tmsrouteondemand:
    post:
      operationId: RouteController_tmsRouteOnDemand
      summary: calculate route on demand
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TmsRouteOnDemandRequestDto'
            examples:
              sample:
                summary: Sample
                value:
                  command: RequestRouteOnDemand
                  reqid: '1001'
                  start:
                    dx: 127.16948
                    dy: 37.53785
                  new:
                    dx: 127.16948
                    dy: 37.53785
                  routeoption: 512
                  destpos:
                    - dx: 127.08671
                      dy: 37.53253
                      nid: 1
                  height: 0
                  weight: 0
                  islast: false
                  nofirst: false
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteResponseDto'
        '201':
          description: ''
      tags: *ref_1
  /calculateRoute:
    post:
      operationId: RouteController_calculateRoute
      summary: calculate route
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateRouteRequestDto'
            examples:
              sample:
                summary: Sample
                value:
                  vehicleType: truck
                  routeType: recommended
                  avoidOptions:
                    - uturn
                    - ferry
                  weight: 2000
                  height: 250
                  sort: enable
                  start:
                    - 37.5164
                    - 127.0495
                  destinations:
                    - locationId: 1
                      location:
                        - 37.50967
                        - 127.02616
                    - locationId: 2
                      location:
                        - 37.51791
                        - 127.03422
                    - locationId: 3
                      location:
                        - 37.51929
                        - 127.03989
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TmsRouteResponseDto'
        '201':
          description: ''
      tags: *ref_1
  /dispatch/version:
    get:
      operationId: DispatchController_getVersion
      parameters: []
      responses:
        '200':
          description: ''
      tags: &ref_2
        - Dispatch APIs
  /locationbaseddispatch:
    post:
      operationId: DispatchController_locationBasedDispatch
      summary: Location based dispatch
      description: 차량의 출발 위치(물류센터)와 화물의 배송지를 기반으로 배송에 소요되는 예상 시간이 일정하도록 군집화한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LocationBasedDispatchDto'
            examples:
              locationBasedDispatchExample:
                summary: Sample
                value:
                  vehicles:
                    - vehicleId: '1001'
                      location:
                        - 37.54649
                        - 127.04329
                    - vehicleId: '1002'
                      location:
                        - 37.55514
                        - 127.13197
                  dispatchType: '1'
                  destinations:
                    - destinationId: '1'
                      location:
                        - 37.54649
                        - 127.04329
                    - destinationId: '2'
                      location:
                        - 37.54578
                        - 127.04246
                    - destinationId: '3'
                      location:
                        - 37.53837
                        - 127.04608
                    - destinationId: '4'
                      location:
                        - 37.54655
                        - 127.04162
                    - destinationId: '5'
                      location:
                        - 37.53837
                        - 127.04608
                    - destinationId: '6'
                      location:
                        - 37.54655
                        - 127.04162
                    - destinationId: '7'
                      location:
                        - 37.54578
                        - 126.94347
                    - destinationId: '8'
                      location:
                        - 37.54578
                        - 127.04694
                    - destinationId: '9'
                      location:
                        - 37.49931
                        - 127.09096
                    - destinationId: '10'
                      location:
                        - 37.60143
                        - 127.0128
                    - destinationId: '11'
                      location:
                        - 37.48415
                        - 126.94347
                    - destinationId: '12'
                      location:
                        - 37.48415
                        - 126.94347
                    - destinationId: '13'
                      location:
                        - 37.48415
                        - 126.94347
                    - destinationId: '14'
                      location:
                        - 37.57447
                        - 126.90174
                    - destinationId: '15'
                      location:
                        - 37.57447
                        - 126.90274
                    - destinationId: '16'
                      location:
                        - 37.5265
                        - 126.87448
                    - destinationId: '17'
                      location:
                        - 37.62874
                        - 127.01978
                    - destinationId: '18'
                      location:
                        - 37.55514
                        - 127.13197
                    - destinationId: '19'
                      location:
                        - 37.54936
                        - 126.87012
                    - destinationId: '20'
                      location:
                        - 37.50255
                        - 127.00936
                    - destinationId: '21'
                      location:
                        - 37.51012
                        - 127.07128
                    - destinationId: '22'
                      location:
                        - 37.55805
                        - 126.86601
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_2
  /sortcandidatewitheta:
    post:
      operationId: DispatchController_sortCandidateWithEta
      summary: Sort candidate with ETA
      description: >-
        배송중 새로운 배송지가 추가되었을 때 배송 차량들의 경로 ETA를 기반으로 가장 적합한 배송 차량에 배송지를 추가한 후 할당된
        배송 차량 정보와 배송지 목록을 업데이트하여 반환한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SortCandidateWithEtaDto'
            examples:
              sortCandidateWithEtaExample:
                summary: Sample
                value:
                  vehicles:
                    - vehicleId: '1001'
                      vehiclePosition:
                        - 37.54649
                        - 127.04329
                      destinations:
                        - destinationId: '1'
                          location:
                            - 37.54649
                            - 127.04329
                        - destinationId: '2'
                          location:
                            - 37.54578
                            - 127.04246
                        - destinationId: '3'
                          location:
                            - 37.53837
                            - 127.04608
                        - destinationId: '4'
                          location:
                            - 37.54655
                            - 127.04162
                        - destinationId: '5'
                          location:
                            - 37.53837
                            - 127.04608
                        - destinationId: '6'
                          location:
                            - 37.54655
                            - 127.04162
                    - vehicleId: '1002'
                      vehiclePosition:
                        - 37.55514
                        - 127.13197
                      destinations:
                        - destinationId: '7'
                          location:
                            - 37.54578
                            - 126.94347
                        - destinationId: '8'
                          location:
                            - 37.54578
                            - 127.04694
                        - destinationId: '9'
                          location:
                            - 37.49931
                            - 127.09096
                        - destinationId: '10'
                          location:
                            - 37.60143
                            - 127.0128
                        - destinationId: '11'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '12'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '13'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '14'
                          location:
                            - 37.57447
                            - 126.90174
                        - destinationId: '15'
                          location:
                            - 37.57447
                            - 126.90274
                        - destinationId: '16'
                          location:
                            - 37.5265
                            - 126.87448
                        - destinationId: '17'
                          location:
                            - 37.62874
                            - 127.01978
                        - destinationId: '18'
                          location:
                            - 37.55514
                            - 127.13197
                        - destinationId: '19'
                          location:
                            - 37.54936
                            - 126.87012
                        - destinationId: '20'
                          location:
                            - 37.50255
                            - 127.00936
                        - destinationId: '21'
                          location:
                            - 37.51012
                            - 127.07128
                  newDestination:
                    destinationId: 3000
                    location:
                      - 37.55805
                      - 126.86601
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_2
  /sortcandidateswithroutes:
    post:
      operationId: DispatchController_sortCandidatesWithRoutes
      summary: Sort candidates with routes
      description: 두지점 (출발지, 도착지) 좌표를 입력 받아 운행하는 차량들의 경로를 기반으로 최적 운송 차량을 결정해 준다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SortCandidatesWithRoutesDto'
            examples:
              sample:
                summary: Sample
                value:
                  fromLocation:
                    - 37.54649
                    - 127.04329
                  toLocation:
                    - 37.54659
                    - 127.04229
                  vehicles:
                    - vehicleId: '1001'
                      vehiclePosition:
                        - 37.54649
                        - 127.04329
                      destinations:
                        - destinationId: '1'
                          location:
                            - 37.54649
                            - 127.04329
                        - destinationId: '2'
                          location:
                            - 37.54578
                            - 127.04246
                        - destinationId: '3'
                          location:
                            - 37.53837
                            - 127.04608
                        - destinationId: '4'
                          location:
                            - 37.54655
                            - 127.04162
                        - destinationId: '5'
                          location:
                            - 37.53837
                            - 127.04608
                        - destinationId: '6'
                          location:
                            - 37.54655
                            - 127.04162
                    - vehicleId: '1002'
                      vehiclePosition:
                        - 37.54649
                        - 127.04329
                      destinations:
                        - destinationId: '7'
                          location:
                            - 37.54578
                            - 126.94347
                        - destinationId: '8'
                          location:
                            - 37.54578
                            - 127.04694
                        - destinationId: '9'
                          location:
                            - 37.49931
                            - 127.09096
                        - destinationId: '10'
                          location:
                            - 37.60143
                            - 127.0128
                        - destinationId: '11'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '12'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '13'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '14'
                          location:
                            - 37.57447
                            - 126.90174
                        - destinationId: '15'
                          location:
                            - 37.57447
                            - 126.90274
                        - destinationId: '16'
                          location:
                            - 37.5265
                            - 126.87448
                        - destinationId: '17'
                          location:
                            - 37.62874
                            - 127.01978
                        - destinationId: '18'
                          location:
                            - 37.55514
                            - 127.13197
                        - destinationId: '19'
                          location:
                            - 37.54936
                            - 126.87012
                        - destinationId: '20'
                          location:
                            - 37.50255
                            - 127.00936
                        - destinationId: '21'
                          location:
                            - 37.51012
                            - 127.07128
                  newDestination:
                    destinationId: 30
                    location:
                      - 37.55805
                      - 126.86601
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_2
  /sortcandidateswithdistance:
    post:
      operationId: DispatchController_sortCandidatesWithDistance
      summary: Sort candidates with distance
      description: 두지점 (출발지, 도착지) 좌표를 입력 받아 운행하는 차량들의 근접 거리를 기반으로 최적 운송 차량을 결정해 준다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SortCandidatesWithDistanceDto'
            examples:
              locationBasedDispatchExample:
                summary: Sample
                value:
                  fromLocation:
                    - 37.54649
                    - 127.04329
                  toLocation:
                    - 37.54659
                    - 127.04229
                  vehicles:
                    - vehicleId: '1001'
                      vehiclePosition:
                        - 37.54649
                        - 127.04329
                      destinations:
                        - destinationId: '1'
                          location:
                            - 37.54649
                            - 127.04329
                        - destinationId: '2'
                          location:
                            - 37.54578
                            - 127.04246
                        - destinationId: '3'
                          location:
                            - 37.53837
                            - 127.04608
                        - destinationId: '4'
                          location:
                            - 37.54655
                            - 127.04162
                        - destinationId: '5'
                          location:
                            - 37.53837
                            - 127.04608
                        - destinationId: '6'
                          location:
                            - 37.54655
                            - 127.04162
                    - vehicleId: '1002'
                      vehiclePosition:
                        - 37.54649
                        - 127.04329
                      destinations:
                        - destinationId: '7'
                          location:
                            - 37.54578
                            - 126.94347
                        - destinationId: '8'
                          location:
                            - 37.54578
                            - 127.04694
                        - destinationId: '9'
                          location:
                            - 37.49931
                            - 127.09096
                        - destinationId: '10'
                          location:
                            - 37.60143
                            - 127.0128
                        - destinationId: '11'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '12'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '13'
                          location:
                            - 37.48415
                            - 126.94347
                        - destinationId: '14'
                          location:
                            - 37.57447
                            - 126.90174
                        - destinationId: '15'
                          location:
                            - 37.57447
                            - 126.90274
                        - destinationId: '16'
                          location:
                            - 37.5265
                            - 126.87448
                        - destinationId: '17'
                          location:
                            - 37.62874
                            - 127.01978
                        - destinationId: '18'
                          location:
                            - 37.55514
                            - 127.13197
                        - destinationId: '19'
                          location:
                            - 37.54936
                            - 126.87012
                        - destinationId: '20'
                          location:
                            - 37.50255
                            - 127.00936
                        - destinationId: '21'
                          location:
                            - 37.51012
                            - 127.07128
                  newDestination:
                    destinationId: 30
                    location:
                      - 37.55805
                      - 126.86601
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_2
  /sorttravelorder:
    post:
      operationId: DispatchController_sortTravelOrder
      summary: Sort travel order
      description: 다수의 방문지 목록을 입력 받아 최적의 방문 순서로 재정열 한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SortTravelOrderDto'
            examples:
              locationBasedDispatchExample:
                summary: Sample
                value:
                  vehiclePosition:
                    - 37.54649
                    - 127.04329
                  destinations:
                    - destinationId: 1
                      location:
                        - 37.54649
                        - 127.04329
                    - destinationId: 2
                      location:
                        - 37.54578
                        - 127.04246
                    - destinationId: 3
                      location:
                        - 37.53837
                        - 127.04608
                    - destinationId: 4
                      location:
                        - 37.54655
                        - 127.04162
                    - destinationId: 5
                      location:
                        - 37.53837
                        - 127.04608
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_2
  /sortvisitorder:
    post:
      operationId: DispatchController_sortVisitOrder
      summary: Sort visit order
      description: WGS84 경위도 좌표들의 방문 순서를 정렬하고 최적 방문 순서에 따른 경로 결과를 반환한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SortVisitOrderDto'
            examples:
              ApiSortVisitOrderExample:
                summary: Sample
                value:
                  vehicleType: truck
                  routeType: recommended
                  avoidOptions:
                    - uturn
                    - ferry
                  weight: 2000
                  height: 250
                  start:
                    - 37.5164
                    - 127.0495
                  destinations:
                    - destinationId: '1'
                      location:
                        - 37.54649
                        - 127.04329
                    - destinationId: '2'
                      location:
                        - 37.54578
                        - 127.04246
                    - destinationId: '3'
                      location:
                        - 37.52837
                        - 127.14608
                    - destinationId: '4'
                      location:
                        - 37.54655
                        - 127.04162
                    - destinationId: '5'
                      location:
                        - 37.53837
                        - 127.04608
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_2
  /cargoplanning:
    post:
      operationId: DispatchController_cargoPlanning
      summary: Cargo Planning - Basic
      description: 기본적으로 kmeans로 방문지 군집화를 통한 화물 계획
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
            examples:
              cargoPlanningExample:
                summary: Sample
                value:
                  vehicles:
                    - vid: 1
                      maxcount: 100
                      dimensions:
                        length: 3000
                        width: 1800
                        height: 1500
                        unit: mm
                      maxweight:
                        value: 1000000
                        unit: g
                      location:
                        dx: 127.04
                        dy: 37.55
                  dests:
                    - nid: 101
                      weight:
                        value: 10
                        unit: kg
                      dimensions:
                        length: 10
                        width: 10
                        height: 10
                        unit: cm
                      count: 1
                      location:
                        dx: 127.041
                        dy: 37.551
                    - nid: 102
                      weight:
                        value: 20
                        unit: kg
                      dimensions:
                        length: 20
                        width: 20
                        height: 20
                        unit: cm
                      count: 2
                      location:
                        dx: 127.042
                        dy: 37.552
                  options:
                    vehicleCount: 1
                    capacity: 100
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/limitedweight:
    post:
      operationId: DispatchController_cargoPlanningLimitedWeight
      summary: Cargo Planning - Limited Weight
      description: 단일 트럭에 복수의 화물을 최대 무게 용량까지 할당. kmeans로 방문지 군집화 후 최대 용량까지 채워 넣음
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/balancedweight:
    post:
      operationId: DispatchController_cargoPlanningBalancedWeight
      summary: Cargo Planning - Balanced Weight
      description: 복수의 트럭에 복수의 화물을 무게에 따라 균등하게 분배. kmeans로 방문지 군집화 후 균등하게 분배
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/limitedvolume:
    post:
      operationId: DispatchController_cargoPlanningLimitedVolume
      summary: Cargo Planning - Limited Volume
      description: 단일 트럭에 복수의 화물을 부피 용량까지 할당. kmeans로 방문지 군집화 후 최대 용량까지 채워 넣음
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/balancedvolume:
    post:
      operationId: DispatchController_cargoPlanningBalancedVolume
      summary: Cargo Planning - Balanced Volume
      description: 복수의 트럭에 복수의 화물을 부피에 따라 균등하게 분배. kmeans로 방문지 군집화 후 균등하게 분배
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/limitedcount:
    post:
      operationId: DispatchController_cargoPlanningLimitedCount
      summary: Cargo Planning - Limited Count
      description: 단일 트럭에 복수의 화물을 개수까지 할당. kmeans로 방문지 군집화 후 최대 개수까지 채워 넣음
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/balancedcount:
    post:
      operationId: DispatchController_cargoPlanningBalancedCount
      summary: Cargo Planning - Balanced Count
      description: 복수의 트럭에 복수의 화물을 개수에 따라 균등하게 분배. kmeans로 방문지 군집화 후 균등하게 분배
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/limitedcombine:
    post:
      operationId: DispatchController_cargoPlanningLimitedCombine
      summary: Cargo Planning - Limited Combine
      description: >-
        단일 트럭에 복수의 화물을 무게, 부피, 개수 용량까지 할당. constraints 옵션의 순서에 따라 우선순위가 바뀜 (기본값:
        weight,volume)
      parameters:
        - name: constraints
          required: true
          in: query
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/balancedcombine:
    post:
      operationId: DispatchController_cargoPlanningBalancedCombine
      summary: Cargo Planning - Balanced Combine
      description: >-
        복수의 트럭에 복수의 화물을 무게, 부피, 개수에 따라 균등하게 분배. constraints 옵션의 순서에 따라 우선순위가 바뀜
        (기본값: weight,volume)
      parameters:
        - name: constraints
          required: true
          in: query
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CargoPlanningRequestDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /cargoplanning/visitorder:
    post:
      operationId: DispatchController_cargoPlanningVisitOrder
      summary: Cargo Planning - Visit Order
      description: >-
        군집화되어 각 트럭에 할당된 화물의 방문 순서를 결정. mode 옵션: nearest, 2opt, 3opt (기본값: 2opt).
        ⚠️ 주의: 이 API는 2단계 프로세스로 사용해야 합니다. 먼저 다른 cargoplanning API로 결과를 생성한 후, 그
        결과를 이 API의 입력으로 사용하세요.
      parameters:
        - name: mode
          required: true
          in: query
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VisitOrderRequestDto'
            examples:
              visitOrderExample:
                summary: Visit Order Input (클러스터링된 결과 JSON)
                description: '⚠️ 중요: 원시 차량/목적지 데이터가 아닌, 이미 클러스터링된 결과 JSON을 입력으로 사용'
                value:
                  result:
                    - location:
                        - 37.55
                        - 127.04
                      nids:
                        - location:
                            - 37.551
                            - 127.041
                          nid: 6001
                        - location:
                            - 37.552
                            - 127.042
                          nid: 6002
                        - location:
                            - 37.553
                            - 127.043
                          nid: 6003
                      nvid: 1
                    - location:
                        - 37.55
                        - 127.04
                      nids:
                        - location:
                            - 37.554
                            - 127.044
                          nid: 6004
                        - location:
                            - 37.555
                            - 127.045
                          nid: 6005
                      nvid: 2
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CargoPlanningResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_2
  /meter/version:
    get:
      operationId: MeterController_getVersion
      parameters: []
      responses:
        '200':
          description: ''
      tags: &ref_3
        - Meter APIs
  /calculateMeterWithLocations:
    post:
      operationId: MeterController_calculateMeterWithLocations
      summary: Calculate distance between two locations
      description: >-
        두 지점(출발지, 도착지) 좌표를 입력 받아 두 지점간의 거리를 옵션에 따라 Euclid Distance, Manhattan
        Distance, Routing Distance 중 한 가지로 계산하여 결과를 제공한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateMeterWithLocationsDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateMeterResponseDto'
        '201':
          description: ''
      tags: *ref_3
  /calculateMeterWithAddresses:
    post:
      operationId: MeterController_calculateMeterWithAddresses
      summary: Calculate distance between two addresses
      description: >-
        두 지점(출발지, 도착지) 주소를 입력 받아 두 지점간의 거리를 옵션에 따라 Euclid Distance, Manhattan
        Distance, Routing Distance 중 한 가지로 계산하여 결과를 제공한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateMeterWithAddressesDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateMeterWithAddressesResponseDto'
        '201':
          description: ''
      tags: *ref_3
  /calculateMeterWithViaLocations:
    post:
      operationId: MeterController_calculateMeterWithViaLocations
      summary: Calculate distance with via locations
      description: >-
        두 지점(출발지, 도착지) Geocode 좌표와 중간 경유지들의 방문순서 정렬된 입력 받아 총 예상거리(Routing
        Distance)를 계산하여 결과를 제공한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateMeterWithViaLocationsDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateMeterWithViaLocationsResponseDto'
        '201':
          description: ''
      tags: *ref_3
  /calculateMeterWithViaAddresses:
    post:
      operationId: MeterController_calculateMeterWithViaAddresses
      summary: Calculate distance with via addresses
      description: >-
        두 지점 (출발지, 도착지) 주소를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과와
        입력된 두 지점의 Geocode 좌표를 제공한다
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateMeterWithViaAddressesDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateMeterWithViaAddressesResponseDto'
        '201':
          description: ''
      tags: *ref_3
  /calculateTravelTimeWithViaLocations:
    post:
      operationId: MeterController_calculateTravelTimeWithViaLocations
      summary: Calculate travel time with via locations
      description: >-
        두 지점(출발지, 도착지) 좌표를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과를
        제공한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateTravelTimeWithViaLocationsDto'
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_3
  /calculateTravelTimeWithAddresses:
    post:
      operationId: MeterController_calculateTravelTimeWithAddresses
      summary: Calculate travel time between addresses
      description: >-
        두 지점 (출발지, 도착지) 주소를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과와
        입력된 두 지점의 Geocode 좌표를 제공한다
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateTravelTimeWithAddressesDto'
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_3
  /calculateTravelTimeWithViaAddresses:
    post:
      operationId: MeterController_calculateTravelTimeWithViaAddresses
      summary: Calculate travel time with via addresses
      description: >-
        두 지점 (출발지, 도착지) 주소를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과와
        입력된 두 지점의 Geocode 좌표를 제공한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateMeterWithViaAddressesDto'
      responses:
        '200':
          description: Successful response
        '201':
          description: ''
      tags: *ref_3
  /calculateMeter:
    post:
      operationId: MeterController_calculateMeter
      summary: Calculate comprehensive distance and time
      description: >-
        출발지와 도착지 그리고 방문순서가 정련된 주소 목록들을 입력 받아 총 방문에 필요한 예상 거리(경로거리)와 운행시간을 계산해
        준다. 각 입력 받은 주소로 Geo-coding을 수행하고 그 계산 결과를 함께 배열로 좌표목록으로 제공한다.
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateMeterDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateMeterWithViaAddressesResponseDto'
        '201':
          description: ''
      tags: *ref_3
  /png:
    get:
      operationId: MapController_getPng
      summary: PNG 타일 이미지 조회
      description: 타일 번호를 받아서 웹맵 타일(PNG)을 제공한다. (PNG 이미지 타일은 현재 한국을 대상으로 서비스되고 있음)
      parameters:
        - name: zoom
          required: true
          in: query
          description: 지도 축척 레벨 (Lv6 ~ Lv18)
          example: 12
          schema:
            type: number
        - name: tilex
          required: true
          in: query
          description: X축 타일 번호 (구글 맵 타일 번호와 같음)
          example: 3493
          schema:
            type: number
        - name: tiley
          required: true
          in: query
          description: Y축 타일 번호 (구글 맵 타일 번호와 같음)
          example: 1586
          schema:
            type: number
        - name: region
          required: true
          in: query
          description: '지역 (default: kor)'
          example: kor
          schema:
            type: string
      responses:
        '200':
          description: PNG 이미지 blob
          content:
            application/json:
              schema:
                type: string
                format: binary
      tags: &ref_4
        - WebMap APIs
  /xvg:
    get:
      operationId: MapController_getXvg
      summary: XVG 타일 데이터 조회
      description: 타일 번호를 받아서 웹맵 타일(압축 SVG)을 제공한다. (압축 SVG 타일은 현재 북미를 대상으로 서비스되고 있음)
      parameters:
        - name: zoom
          required: true
          in: query
          description: 지도 축척 레벨 (Lv4 ~ Lv18)
          example: 12
          schema:
            type: number
        - name: tilex
          required: true
          in: query
          description: X축 타일 번호 (구글 맵 타일 번호와 같음)
          example: 1206
          schema:
            type: number
        - name: tiley
          required: true
          in: query
          description: Y축 타일 번호 (구글 맵 타일 번호와 같음)
          example: 1540
          schema:
            type: number
        - name: region
          required: true
          in: query
          description: '지역 (default: kor)'
          example: nam
          schema:
            type: string
      responses:
        '200':
          description: 압축된 SVG blob
          content:
            application/json:
              schema:
                type: string
                format: binary
      tags: *ref_4
  /pngatpoint:
    get:
      operationId: MapController_getPngAtPoint
      summary: 특정 좌표의 PNG 이미지 조회
      description: 지도 레벨과 경위도 좌표를 받아서 웹맵 타일(PNG)을 제공한다. (PNG 이미지 타일은 현재 한국을 대상으로 서비스되고 있음)
      parameters:
        - name: zoom
          required: true
          in: query
          description: 지도 축척 레벨 (Lv6 ~ Lv18)
          example: 12
          schema:
            type: number
        - name: lat
          required: true
          in: query
          description: 위도 (WGS84)
          example: 37.542874
          schema:
            type: number
        - name: lng
          required: true
          in: query
          description: 경도 (WGS84)
          example: 127.044929
          schema:
            type: number
        - name: region
          required: true
          in: query
          description: '지역 (default: kor)'
          example: kor
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PngAtPointResponseDto'
      tags: *ref_4
  /pnginrange:
    get:
      operationId: MapController_getPngInRange
      summary: 특정 범위의 PNG 이미지 조회
      description: >-
        지도 레벨, 경위도 좌표 그리고 검색 범위를 받아서 웹맵 타일(PNG)을 제공한다. (PNG 이미지 타일은 현재 한국을 대상으로
        서비스되고 있음)
      parameters:
        - name: zoom
          required: true
          in: query
          description: 지도 축척 레벨 (Lv6 ~ Lv18)
          example: 12
          schema:
            type: number
        - name: lat
          required: true
          in: query
          description: 위도 (WGS84)
          example: 37.542874
          schema:
            type: number
        - name: lng
          required: true
          in: query
          description: 경도 (WGS84)
          example: 127.044929
          schema:
            type: number
        - name: width
          required: true
          in: query
          description: 검색 범위 넓이 (~ 2048px)
          example: 256
          schema:
            type: number
        - name: height
          required: true
          in: query
          description: 검색 범위 높이 (~ 2048px)
          example: 256
          schema:
            type: number
        - name: region
          required: true
          in: query
          description: '지역 (default: kor)'
          example: kor
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PngInRangeResponseDto'
      tags: *ref_4
  /getmapversion:
    get:
      operationId: MapController_getMapVersion
      summary: 맵 버전 조회
      description: 맵 데이터 버전을 조회한다.
      parameters:
        - name: extn
          required: true
          in: query
          description: 타일타입 (png 또는 xvg)
          example: png
          schema:
            type: string
        - name: region
          required: true
          in: query
          description: 지역 (kor 또는 nam)
          example: kor
          schema:
            type: string
      responses:
        '200':
          description: 맵 버전 정보
          content:
            application/json:
              schema:
                type: string
      tags: *ref_4
info:
  title: Logisteq LBS Core API
  description: Location-based services API for North America region
  version: '1.0'
  contact: {}
tags: []
servers:
  - url: http://localhost:15001
  - url: https://map-tile2-qa.logisteq.com
components:
  schemas:
    LocationCoordinate:
      type: object
      properties:
        latitude:
          type: number
          description: 위도
          example: 37.5109
        longitude:
          type: number
          description: 경도
          example: 126.94196
      required:
        - latitude
        - longitude
    SearchAddressResult:
      type: object
      properties:
        coordinates:
          description: 검색 결과 위치의 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
        adminarea:
          type: string
          description: 광역시/도 정보
          example: 서울특별시
        subadminarea:
          type: string
          description: 시/군/구 정보
          example: 종로구
        locality:
          type: string
          description: 읍/면/동 정보 또는 도로명
          example: 봉익동
        sublocality:
          type: string
          description: 리 정보 (없을 경우 빈 문자열)
          example: ''
        subno:
          type: string
          description: 지번 정보 또는 건물 번호
          example: '169'
        intersectinglocality:
          type: string
          description: 전체 주소 또는 장소명
          example: 서울특별시 종로구 돈화문로6가길 2
        distance:
          type: number
          description: 검색 요청 위치와의 거리 (미터)
          example: 5561
        postalcode:
          type: string
          description: 우편번호
          example: '03138'
        entrance:
          description: 도보 입구점 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
        entrancevehicle:
          description: 차량 입구점 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
      required:
        - coordinates
        - adminarea
        - subadminarea
        - locality
        - sublocality
        - subno
        - intersectinglocality
        - distance
        - postalcode
        - entrance
        - entrancevehicle
    SearchAddressResponseDto:
      type: object
      properties:
        count:
          type: number
          description: 검색 결과 수
          example: 1
        searchaddress:
          description: 주소 검색 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/SearchAddressResult'
      required:
        - count
        - searchaddress
    SearchResponseDto:
      type: object
      properties:
        count:
          type: number
          description: 검색 결과 수
          example: 1
        search:
          description: 통합 검색 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/SearchAddressResult'
      required:
        - count
        - search
    SearchCoordResponseDto:
      type: object
      properties:
        count:
          type: number
          description: 검색 결과 수
          example: 1
        searchcoord:
          description: 좌표 검색 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/SearchAddressResult'
      required:
        - count
        - searchcoord
    SearchEntranceResponseDto:
      type: object
      properties:
        xpos:
          type: string
          description: 입구점 X 좌표 (경도)
          example: '127.026794'
        ypos:
          type: string
          description: 입구점 Y 좌표 (위도)
          example: '37.615124'
      required:
        - xpos
        - ypos
    GeocodingRequestDto:
      type: object
      properties:
        count:
          type: number
          description: 지오코딩 요청 갯수
          example: 3
        addr:
          description: 지오코딩 요청 주소
          example:
            - 수원시 영통구 망포동 142-3
            - 서울특별시 송파구 오금로 310 중앙일보 강남공장
            - 서울특별시 서초구 효령로 182
          type: array
          items:
            type: string
      required:
        - count
        - addr
    GeocodingResponseDto:
      type: object
      properties:
        count:
          type: number
          description: 지오코딩 결과 수
          example: 1
        geocoding:
          description: 지오코딩 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/SearchAddressResult'
      required:
        - count
        - geocoding
    SearchAreaResponseDto:
      type: object
      properties:
        areacoordinates:
          description: 검색 결과 폴리곤 좌표 목록
          example:
            - - 37.5109
              - 126.94196
            - - 37.511
              - 126.94197
            - - 37.5111
              - 126.94198
            - - 37.5109
              - 126.94196
          type: array
          items:
            type: object
        areatype:
          type: string
          description: 검색 결과 영역 유형
          example: building
        count:
          type: string
          description: 검색 결과 수
          example: '1'
      required:
        - areacoordinates
        - areatype
        - count
    SearchPoiItemDto:
      type: object
      properties:
        coordinates:
          description: 검색 결과 위치의 WGS 84 좌표 [경도, 위도]
          example:
            - 127.04637
            - 37.54363
          type: array
          items:
            type: number
        adminarea:
          type: string
          description: 광역시/도 정보
          example: 서울특별시
        subadminarea:
          type: string
          description: 시/군/구 정보
          example: 성동구
        locality:
          type: string
          description: 읍/면/동 정보 또는 도로명
          example: 성수동1가
        sublocality:
          type: string
          description: 리 정보 (없을 경우 빈 문자열)
          example: ''
        subno:
          type: string
          description: 지번 또는 건물번호
          example: 656-3
        intersectinglocality:
          type: string
          description: 전체 주소
          example: 서울특별시 성동구 왕십리로 58
        facilityid:
          type: string
          description: 시설물 ID (없을 경우 빈 문자열)
          example: ''
        telephone:
          type: string
          description: 전화번호 (없을 경우 빈 문자열)
          example: ''
        distance:
          type: number
          description: 검색 요청 위치와의 거리 (미터)
          example: 168
        postalcode:
          type: string
          description: 우편번호
          example: '04778'
        bdongcode:
          type: string
          description: 법정동코드
          example: '1120011400'
        entrance:
          description: 건물 도보 입구점 WGS 84 좌표 [경도, 위도]
          example:
            - 127.046567
            - 37.543588
          type: array
          items:
            type: number
        entrancevehicle:
          description: 건물 차량 입구점 WGS 84 좌표 [경도, 위도] (없을 경우 [0, 0])
          example:
            - 0
            - 0
          type: array
          items:
            type: number
        newRoadName:
          type: string
          description: 도로명
          example: 왕십리로
        eupMyeonDong:
          type: string
          description: 읍면동명
          example: 성수동1가
        bonBu:
          type: string
          description: 지번 본번
          example: '656'
        buildingNum:
          type: string
          description: 건물번호
          example: '58'
        pOIName:
          type: string
          description: POI 명칭
          example: 서울숲포휴
        adminAreaRdx:
          type: string
          description: 광역시/도 축약명
          example: 서울
        subAdminAreaRdx:
          type: string
          description: 시/군/구 축약명
          example: 성동
        localityRdx:
          type: string
          description: 읍/면/동 축약명
          example: 성수동1
        newRoadNameRdx:
          type: string
          description: 도로명 축약명
          example: 왕십리로
        intersectingStreetLocalityRdx:
          type: string
          description: 도로명 주소 축약명
          example: 서울 성동 성수동1
        intersectingStreetLocality2Rdx:
          type: string
          description: 도로명 주소 축약명2
          example: 서울 성동 왕십리로
      required:
        - coordinates
        - adminarea
        - subadminarea
        - locality
        - sublocality
        - subno
        - intersectinglocality
        - facilityid
        - telephone
        - distance
        - postalcode
        - bdongcode
        - entrance
        - entrancevehicle
        - newRoadName
        - eupMyeonDong
        - bonBu
        - buildingNum
        - pOIName
        - adminAreaRdx
        - subAdminAreaRdx
        - localityRdx
        - newRoadNameRdx
        - intersectingStreetLocalityRdx
        - intersectingStreetLocality2Rdx
    SearchPoiResponseDto:
      type: object
      properties:
        count:
          type: number
          description: 검색 결과 수
          example: 50
        searchpoi:
          description: POI 검색 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/SearchPoiItemDto'
      required:
        - count
        - searchpoi
    DistrictCodeDto:
      type: object
      properties:
        distcode:
          type: number
          description: 행정동 코드
          example: 1111000000
        distname:
          type: string
          description: 행정동 이름
          example: 종로구
      required:
        - distcode
        - distname
    SearchDistCodeResponseDto:
      type: object
      properties:
        distlist:
          description: 행정동 코드 검색 결과 배열
          type: array
          items:
            $ref: '#/components/schemas/DistrictCodeDto'
      required:
        - distlist
    Coordinate:
      type: object
      properties:
        dx:
          type: number
          description: X 좌표 (경도, WGS84 좌표계)
          example: 127.04498
        dy:
          type: number
          description: Y 좌표 (위도, WGS84 좌표계)
          example: 37.54253
      required:
        - dx
        - dy
    RequestCoordinate:
      type: object
      properties:
        dx:
          type: number
          description: X 좌표 (경도, WGS84 좌표계)
          example: 127.04498
        dy:
          type: number
          description: Y 좌표 (위도, WGS84 좌표계)
          example: 37.54253
        nid:
          type: number
          description: 노드 ID
          example: 10421614
        type:
          type: string
          description: 위치 타입
          example: GENERAL
        postalCode:
          type: string
          description: 우편번호
          example: '05029'
      required:
        - dx
        - dy
    TmsRouteRequestDto:
      type: object
      properties:
        command:
          type: string
          description: Route Core 동작요청을 구분함
          example: RequestRoute
        start:
          description: 출발 좌표
          allOf:
            - $ref: '#/components/schemas/Coordinate'
        reqid:
          type: string
          description: 요청에 대한 고유번호
          example: '1600658759187'
        routeoption:
          type: number
          description: |-
            경로탐색 옵션
            차량종류, 경로순서 배정 기준, 경로 계산 시 적용 옵션을 설정
          example: 512
        destpos:
          description: 목적지 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/RequestCoordinate'
        height:
          type: number
          description: 차량 높이 (cm), 트럭 경로 시 필수
          example: 0
        weight:
          type: number
          description: 차량 무게 (kg), 트럭 경로 시 필수
          example: 0
      required:
        - command
        - start
        - reqid
        - routeoption
        - destpos
    RouteOrder:
      type: object
      properties:
        nid:
          type: string
          description: 목적지 구분번호
          example: '1'
        visit:
          type: number
          description: 방문 순서
          example: 1
        estimate:
          type: number
          description: 도착예정시간(초)
          example: 319
        dist:
          type: number
          description: 도달거리(미터)
          example: 951
      required:
        - nid
        - visit
        - estimate
        - dist
    TmsRouteResponseDto:
      type: object
      properties:
        command:
          type: string
          description: 명령어
          example: ResponseRoute
        resid:
          type: number
          description: 응답 ID
          example: 1
        order:
          description: 방문 순서 정보
          type: array
          items:
            $ref: '#/components/schemas/RouteOrder'
        pnts:
          type: object
          description: 경로 선형(좌표) 정보
      required:
        - command
        - resid
        - order
        - pnts
    TmsRouteListRequestDto:
      type: object
      properties:
        command:
          type: string
          description: Route Core 동작요청을 구분함
          example: RequestRouteList
        routeoption:
          type: number
          description: 경로탐색 옵션
          example: 512
        routecount:
          type: number
          description: 다중 경로탐색을 요청정보 개수
          example: 3
        routelist:
          description: 다중 경로탐색 요청정보 배열
          type: array
          items:
            $ref: '#/components/schemas/TmsRouteRequestDto'
      required:
        - command
        - routeoption
        - routecount
        - routelist
    TmsRouteListResponseDto:
      type: object
      properties:
        command:
          type: string
          description: 명령어
          example: ResponseRouteList
        routecount:
          type: number
          description: 경로 수
          example: 3
        routelist:
          description: 경로 정보
          type: array
          items:
            $ref: '#/components/schemas/TmsRouteResponseDto'
      required:
        - command
        - routecount
        - routelist
    TmsRouteOnDemandRequestDto:
      type: object
      properties:
        command:
          type: string
          description: Route Core 동작요청을 구분함
          example: RequestRoute
        start:
          description: 출발 좌표
          allOf:
            - $ref: '#/components/schemas/Coordinate'
        reqid:
          type: string
          description: 요청에 대한 고유번호
          example: '1600658759187'
        routeoption:
          type: number
          description: |-
            경로탐색 옵션
            차량종류, 경로순서 배정 기준, 경로 계산 시 적용 옵션을 설정
          example: 512
        destpos:
          description: 목적지 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/RequestCoordinate'
        height:
          type: number
          description: 차량 높이 (cm), 트럭 경로 시 필수
          example: 0
        weight:
          type: number
          description: 차량 무게 (kg), 트럭 경로 시 필수
          example: 0
        new:
          description: 새로운 방문지 좌표
          allOf:
            - $ref: '#/components/schemas/Coordinate'
        islast:
          type: boolean
          description: 새로운 방문지를 마지막에 방문
          example: false
        nofirst:
          type: boolean
          description: 새로운 방문지를 첫 번째로 방문하지 않음
          example: false
      required:
        - command
        - start
        - reqid
        - routeoption
        - destpos
        - new
    CalculateRouteRequestDto:
      type: object
      properties:
        vehicleType:
          type: string
          description: 차량 유형
          example: truck
          enum:
            - car
            - truck
            - motorcycle
        sort:
          type: string
          description: 정렬 옵션
          example: enable
          enum:
            - enable
            - straight
            - manhattan
            - routeDistance
            - routeTime
            - disable
        avoidOptions:
          description: 회피 옵션
          example:
            - uturn
            - ferry
          type: array
          items:
            type: string
        weight:
          type: number
          description: 차량 무게 (kg), 트럭 경로 시 필수
          example: 2000
        height:
          type: number
          description: 차량 높이 (cm), 트럭 경로 시 필수
          example: 250
        start:
          description: 출발 좌표
          allOf:
            - $ref: '#/components/schemas/Coordinate'
        destinations:
          description: 목적지 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/Coordinate'
      required:
        - vehicleType
        - sort
        - avoidOptions
        - start
        - destinations
    DestinationInfo:
      type: object
      properties:
        destinationId:
          type: string
          description: 배송지 ID
          example: '1'
        location:
          description: 배송지 위치
          example:
            - 37.54649
            - 127.04329
          type: array
          items:
            type: number
      required:
        - destinationId
        - location
    VehicleInfo:
      type: object
      properties:
        vehicleId:
          type: string
          description: 배송차량 ID
          example: '1001'
        location:
          description: 배송차량 위치
          example:
            - 37.54649
            - 127.04329
          type: array
          items:
            type: number
        destinations:
          description: 현재 할당된 배송지 목록
          type: array
          items:
            $ref: '#/components/schemas/DestinationInfo'
      required:
        - vehicleId
        - location
    LocationBasedDispatchDto:
      type: object
      properties:
        dispatchType:
          type: string
          description: 배차 타입
          example: localizedDistribution
          enum:
            - localizedDistribution
            - equalDistribution
        vehicles:
          description: 차량 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        vehicleCount:
          type: number
          description: 차량 대수
          example: 2
        destinations:
          description: 배송지 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/DestinationInfo'
      required:
        - dispatchType
        - vehicles
        - destinations
    SortCandidateWithEtaDto:
      type: object
      properties:
        vehicles:
          description: 차량 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        newDestination:
          description: 새 배송지 정보
          allOf:
            - $ref: '#/components/schemas/DestinationInfo'
      required:
        - vehicles
        - newDestination
    Location:
      type: object
      properties:
        latitude:
          type: number
          description: 위도
          example: 37.54649
        longitude:
          type: number
          description: 경도
          example: 127.04329
      required:
        - latitude
        - longitude
    SortCandidatesWithRoutesDto:
      type: object
      properties:
        fromLocation:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/Location'
        toLocation:
          description: 도착지 좌표
          allOf:
            - $ref: '#/components/schemas/Location'
        vehicles:
          description: 차량 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        newDestination:
          description: 새 배송지 정보
          allOf:
            - $ref: '#/components/schemas/DestinationInfo'
      required:
        - fromLocation
        - toLocation
        - vehicles
        - newDestination
    SortCandidatesWithDistanceDto:
      type: object
      properties:
        fromLocation:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/Location'
        toLocation:
          description: 도착지 좌표
          allOf:
            - $ref: '#/components/schemas/Location'
        vehicles:
          description: 차량 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/VehicleInfo'
        newDestination:
          description: 새 배송지 정보
          allOf:
            - $ref: '#/components/schemas/DestinationInfo'
      required:
        - fromLocation
        - toLocation
        - vehicles
        - newDestination
    SortTravelOrderDto:
      type: object
      properties:
        vehiclePosition:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/Location'
        destinations:
          description: 배송지 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/DestinationInfo'
      required:
        - vehiclePosition
        - destinations
    SortVisitOrderDto:
      type: object
      properties:
        vehicleType:
          type: string
          description: 차량 유형
          enum:
            - car
            - truck
            - motorcycle
        routeType:
          type: string
          description: 경로 유형
          enum:
            - recommended
            - shortest
            - ecoroute
        avoidOptions:
          description: 회피 옵션
          type: array
          items:
            type: string
        weight:
          type: number
          description: 차량 무게 (kg)
          example: 2000
        height:
          type: number
          description: 차량 높이 (cm)
          example: 250
        start:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/Location'
        destinations:
          description: 배송지 정보 배열
          type: array
          items:
            $ref: '#/components/schemas/DestinationInfo'
      required:
        - vehicleType
        - routeType
        - avoidOptions
        - start
        - destinations
    DimensionsDto:
      type: object
      properties:
        length:
          type: number
          description: 길이
          example: 3000
        width:
          type: number
          description: 너비
          example: 1800
        height:
          type: number
          description: 높이
          example: 1500
        unit:
          type: string
          description: 단위 (mm, cm, m)
          example: mm
      required:
        - length
        - width
        - height
        - unit
    WeightDto:
      type: object
      properties:
        value:
          type: number
          description: 무게 값
          example: 1000000
        unit:
          type: string
          description: 무게 단위 (g, kg, t)
          example: g
      required:
        - value
        - unit
    CargoLocationDto:
      type: object
      properties:
        dx:
          type: number
          description: X 좌표 (경도)
          example: 127.04
        dy:
          type: number
          description: Y 좌표 (위도)
          example: 37.55
      required:
        - dx
        - dy
    VehicleDto:
      type: object
      properties:
        vid:
          type: number
          description: 차량 ID
          example: 1
        maxcount:
          type: number
          description: 최대 수용 개수
          example: 100
        dimensions:
          description: 차량 크기
          allOf:
            - $ref: '#/components/schemas/DimensionsDto'
        maxweight:
          description: 최대 무게
          allOf:
            - $ref: '#/components/schemas/WeightDto'
        location:
          description: 차량 위치
          allOf:
            - $ref: '#/components/schemas/CargoLocationDto'
      required:
        - vid
        - location
    DestinationDto:
      type: object
      properties:
        nid:
          type: number
          description: 목적지 ID
          example: 101
        weight:
          description: 화물 무게
          allOf:
            - $ref: '#/components/schemas/WeightDto'
        dimensions:
          description: 화물 크기
          allOf:
            - $ref: '#/components/schemas/DimensionsDto'
        count:
          type: number
          description: 화물 개수
          example: 1
        location:
          description: 목적지 위치
          allOf:
            - $ref: '#/components/schemas/CargoLocationDto'
      required:
        - nid
        - location
    CargoPlanningOptionsDto:
      type: object
      properties:
        vehicleCount:
          type: number
          description: 차량 대수
          example: 1
        capacity:
          type: number
          description: 용량
          example: 100
      required:
        - vehicleCount
        - capacity
    CargoPlanningRequestDto:
      type: object
      properties:
        vehicles:
          description: 차량 목록
          type: array
          items:
            $ref: '#/components/schemas/VehicleDto'
        dests:
          description: 목적지 목록
          type: array
          items:
            $ref: '#/components/schemas/DestinationDto'
        options:
          description: 옵션
          allOf:
            - $ref: '#/components/schemas/CargoPlanningOptionsDto'
      required:
        - vehicles
        - dests
        - options
    AssignedDestinationDto:
      type: object
      properties:
        location:
          description: 목적지 위치 [위도, 경도]
          example:
            - 37.551
            - 127.041
          type: array
          items:
            type: number
        nid:
          type: number
          description: 목적지 ID
          example: 101
      required:
        - location
        - nid
    CargoPlanningResultDto:
      type: object
      properties:
        location:
          description: 차량 위치 [위도, 경도]
          example:
            - 37.55
            - 127.04
          type: array
          items:
            type: number
        nids:
          description: 할당된 목적지 목록
          type: array
          items:
            $ref: '#/components/schemas/AssignedDestinationDto'
        nvid:
          type: number
          description: 차량 ID
          example: 1
      required:
        - location
        - nids
        - nvid
    CargoPlanningResponseDto:
      type: object
      properties:
        command:
          type: string
          description: 명령어
          example: cargoplanning
        result:
          description: 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/CargoPlanningResultDto'
      required:
        - command
        - result
    VisitOrderRequestDto:
      type: object
      properties:
        result:
          description: 이미 클러스터링된 결과 JSON
          type: array
          items:
            $ref: '#/components/schemas/CargoPlanningResultDto'
      required:
        - result
    CalculateMeterWithLocationsDto:
      type: object
      properties:
        fromLocation:
          description: 출발지 좌표 [위도, 경도]
          example:
            - 37.5109
            - 126.94196
          type: array
          items:
            type: number
        toLocation:
          description: 도착지 좌표 [위도, 경도]
          example:
            - 37.54578
            - 127.04694
          type: array
          items:
            type: number
        option:
          type: object
          description: 거리 계산 옵션
          example: euclid
      required:
        - fromLocation
        - toLocation
        - option
    CalculateMeterResponseDto:
      type: object
      properties:
        totalDistance:
          type: number
          description: 총 거리 (미터)
          example: 10234
        estimatedTime:
          type: number
          description: 예상 소요 시간 (초)
          example: 1234
        routeCoordinates:
          description: 경로 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/LocationCoordinate'
      required:
        - totalDistance
        - estimatedTime
        - routeCoordinates
    CalculateMeterWithAddressesDto:
      type: object
      properties:
        fromAddress:
          type: string
          description: 출발지 주소
          example: 서울특별시 강남구 언주로30길 56
        toAddress:
          type: string
          description: 도착지 주소
          example: 서울특별시 성동구 왕십리로 58
        option:
          type: object
          description: 거리 계산 옵션
          example: route
      required:
        - fromAddress
        - toAddress
        - option
    CalculateMeterWithAddressesResponseDto:
      type: object
      properties:
        totalDistance:
          type: number
          description: 총 거리 (미터)
          example: 10234
        estimatedTime:
          type: number
          description: 예상 소요 시간 (초)
          example: 1234
        routeCoordinates:
          description: 경로 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/LocationCoordinate'
        fromLocation:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
        toLocation:
          description: 도착지 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
      required:
        - totalDistance
        - estimatedTime
        - routeCoordinates
        - fromLocation
        - toLocation
    CalculateMeterWithViaLocationsDto:
      type: object
      properties:
        fromLocation:
          description: 출발지 좌표 [위도, 경도]
          example:
            - 37.5109
            - 126.94196
          type: array
          items:
            type: number
        viaLocations:
          type: array
          items:
            required: true
            description: 경유지 좌표 목록 [[위도, 경도], ...]
            example:
              - - 37.61481
                - 126.94196
              - - 37.64814
                - 127.023
            type: array
            items:
              type: number
        toLocation:
          description: 도착지 좌표 [위도, 경도]
          example:
            - 37.50385
            - 127.04679
          type: array
          items:
            type: number
        option:
          type: object
          description: 거리 계산 옵션
          example: route
      required:
        - fromLocation
        - viaLocations
        - toLocation
        - option
    CalculateMeterWithViaLocationsResponseDto:
      type: object
      properties:
        totalDistance:
          type: number
          description: 총 거리 (미터)
          example: 10234
        estimatedTime:
          type: number
          description: 예상 소요 시간 (초)
          example: 1234
        routeCoordinates:
          description: 경로 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/LocationCoordinate'
        viaDistances:
          description: 경유지 거리 목록 (미터)
          example: &ref_5
            - 1234
            - 2345
            - 3456
          type: array
          items:
            type: number
      required:
        - totalDistance
        - estimatedTime
        - routeCoordinates
        - viaDistances
    CalculateMeterWithViaAddressesDto:
      type: object
      properties:
        fromAddress:
          type: string
          description: 출발지 주소
          example: 서울특별시 성동구 왕십리로 58
        viaAddresses:
          description: 경유지 주소 목록
          example:
            - 서울특별시 강남구 언주로30길 56
            - 서울특별시 강남구 언주로30길 57
          type: array
          items:
            type: string
        toAddress:
          type: string
          description: 도착지 주소
          example: 서울시 서초구 남부순환로 325길 9
        option:
          type: object
          description: 거리 계산 옵션
          example: euclid
      required:
        - fromAddress
        - viaAddresses
        - toAddress
        - option
    CalculateMeterWithViaAddressesResponseDto:
      type: object
      properties:
        totalDistance:
          type: number
          description: 총 거리 (미터)
          example: 10234
        estimatedTime:
          type: number
          description: 예상 소요 시간 (초)
          example: 1234
        routeCoordinates:
          description: 경로 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/LocationCoordinate'
        viaDistances:
          description: 경유지 거리 목록 (미터)
          example: *ref_5
          type: array
          items:
            type: number
        fromLocation:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
        viaLocations:
          description: 경유지 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/LocationCoordinate'
        toLocation:
          description: 도착지 좌표
          allOf:
            - $ref: '#/components/schemas/LocationCoordinate'
      required:
        - totalDistance
        - estimatedTime
        - routeCoordinates
        - viaDistances
        - fromLocation
        - viaLocations
        - toLocation
    LocationDto:
      type: object
      properties:
        coordinates:
          description: 좌표 [위도, 경도]
          example:
            - 37.5109
            - 126.94196
          type: array
          items:
            type: number
      required:
        - coordinates
    CalculateTravelTimeWithViaLocationsDto:
      type: object
      properties:
        fromLocation:
          description: 출발지 좌표
          allOf:
            - $ref: '#/components/schemas/LocationDto'
        viaLocations:
          description: 경유지 좌표 목록
          type: array
          items:
            $ref: '#/components/schemas/LocationDto'
        toLocation:
          description: 도착지 좌표
          allOf:
            - $ref: '#/components/schemas/LocationDto'
      required:
        - fromLocation
        - viaLocations
        - toLocation
    CalculateTravelTimeWithAddressesDto:
      type: object
      properties:
        fromAddress:
          type: string
          description: 출발지 주소
          example: 서울특별시 강남구 언주로30길 56
        toAddress:
          type: string
          description: 도착지 주소
          example: 서울특별시 성동구 왕십리로 58
      required:
        - fromAddress
        - toAddress
    CalculateMeterDto:
      type: object
      properties:
        fromAddress:
          type: string
          description: 출발지 주소
          example: 서울특별시 강남구 언주로30길 56
        viaAddresses:
          description: 경유지 주소 목록
          example:
            - 서울특별시 강남구 언주로30길 56
            - 서울특별시 강남구 언주로30길 57
          type: array
          items:
            type: string
        toAddress:
          type: string
          description: 도착지 주소
          example: 서울특별시 성동구 왕십리로 58
        vehicleType:
          type: object
          description: 차량 유형
          example: car
        sort:
          type: object
          description: 정렬 옵션
          example: routeTime
      required:
        - fromAddress
        - viaAddresses
        - toAddress
        - vehicleType
        - sort
    TileSize:
      type: object
      properties:
        width:
          type: number
          description: 타일 넓이 (px)
          example: 256
        height:
          type: number
          description: 타일 높이 (px)
          example: 256
      required:
        - width
        - height
    PngAtPointItem:
      type: object
      properties:
        offsetX:
          type: string
          description: 요청된 경도 위치 (타일 이미지 내 경도 위치, 0.0 ~ 1.0)
          example: '0.489'
        offsetY:
          type: string
          description: 요청된 위도 위치 (타일 이미지 내 위도 위치, 0.0 ~ 1.0)
          example: '0.524'
        base64Png:
          type: string
          description: base64로 인코딩된 png 타일 이미지
          example: data:image/png;base64,iVB...mCC
      required:
        - offsetX
        - offsetY
        - base64Png
    PngAtPointResponseDto:
      type: object
      properties:
        resultCode:
          type: string
          description: '결과 코드 (''00'': success, ''01'': error)'
          example: '00'
        resultMsg:
          type: string
          description: 결과 메시지
          example: success
        tileSize:
          description: 맵 타일 사이즈
          allOf:
            - $ref: '#/components/schemas/TileSize'
        count:
          type: number
          description: 결과 목록 수 (0 또는 1)
          example: 1
        items:
          description: 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/PngAtPointItem'
      required:
        - resultCode
        - resultMsg
        - tileSize
        - count
        - items
    PngInRangeItem:
      type: object
      properties:
        x:
          type: number
          description: 스크린 좌표 X (검색 범위가 스크린일 경우 타일의 위치)
          example: -254
        'y':
          type: number
          description: 스크린 좌표 Y (검색 범위가 스크린일 경우 타일의 위치)
          example: -7
        base64Png:
          type: string
          description: base64로 인코딩된 png 타일 이미지
          example: data:image/png;base64,iVB...mCC
      required:
        - x
        - 'y'
        - base64Png
    PngInRangeResponseDto:
      type: object
      properties:
        resultCode:
          type: string
          description: '결과 코드 (''00'': success, ''01'': error)'
          example: '00'
        resultMsg:
          type: string
          description: 결과 메시지
          example: success
        tileSize:
          description: 맵 타일 사이즈
          allOf:
            - $ref: '#/components/schemas/TileSize'
        count:
          type: number
          description: 결과 목록 수 (0...n)
          example: 4
        items:
          description: 결과 목록
          type: array
          items:
            $ref: '#/components/schemas/PngInRangeItem'
      required:
        - resultCode
        - resultMsg
        - tileSize
        - count
        - items
