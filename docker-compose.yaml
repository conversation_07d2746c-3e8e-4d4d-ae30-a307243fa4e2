version: '3.8'

services:
  lbs-proxy:
    image: 232038870714.dkr.ecr.ap-northeast-2.amazonaws.com/logisteq/lbs-proxy:latest
    container_name: lbs-proxy
    ports:
      - "3003:3003"
    environment:
      - API_BASE_URL=http://lbs-core-dev.logisteq.com
      - NODE_ENV=production
      - PORT=3003
      - SEARCH_SERVERS="http://lbs-core-dev.logisteq.com:15002,http://aloa-core2.logisteq.com:31502"
      - ROUTE_SERVERS="http://lbs-core-dev.logisteq.com:15003,http://aloa-core2.logisteq.com:31503"
      - METER_SERVERS="http://lbs-core-dev.logisteq.com:15003,http://aloa-core2.logisteq.com:31503"
      - DISPATCH_SERVERS="http://lbs-core-dev.logisteq.com:15004,http://aloa-core2.logisteq.com:31504"
      - SEARCH_WEIGHTS="1,1"
      - ROUTE_WEIGHTS="1,1"
      - METER_WEIGHTS="1,1"
      - DISPATCH_WEIGHTS="1,1"
    volumes:
      - ./logs:/usr/src/app/logs
    restart: unless-stopped
    networks:
      - lbs-proxy-network

networks:
  lbs-proxy-network:
    driver: bridge

# Uncomment if you're using volumes
# volumes:
#   postgres-data: