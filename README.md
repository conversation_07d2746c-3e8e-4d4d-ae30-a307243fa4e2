# LBS Proxy 서버

## 개요

LBS Proxy 서버는 Location Based Service (LBS) 시스템의 안정성, 보안성, 그리고 확장성을 향상시키기 위한 프록시 역할을 수행합니다. 이 서버는 로드 밸런싱, 장애 복구, 보안 강화, 그리고 유연한 기능 확장을 제공하여 LBS 시스템의 전반적인 성능과 신뢰성을 개선합니다.

## 주요 기능

### 1. 로드 밸런싱

- 다수의 서버 등록을 통한 효율적인 부하 분산
- 최적화된 리소스 활용으로 시스템 성능 향상

### 2. 장애 복구 메커니즘

- 요청 실패 시 다른 LBS 서버로 자동 전환
- 설정 가능한 N회의 재시도 로직으로 안정성 확보

### 3. 보안 강화

- 기존 외부 노출 LBS Endpoint(포트 15002, 15003)의 내부 URL 통신 전환
- SSL 인증 지원으로 보안 통신 가능 (추가 설정 필요)
- LBS 서버의 단일 엔드포인트 설정

### 4. 최적화된 경로 탐색 (신규)

- **우편번호 기반 그룹핑**: 배송지를 우편번호별로 자동 그룹핑하여 효율적인 경로 계산
- **대용량 요청 처리**: 많은 배송지가 포함된 요청을 작은 그룹으로 분할하여 처리
- **지능형 그룹 순서 결정**: 시작점으로부터의 거리를 기반으로 최적의 그룹 방문 순서 계산
- **성능 향상**: API 호출 횟수 최적화로 응답 시간 단축 및 오류율 감소
- **유연한 설정**: 그룹 크기 조정, 우편번호 그룹핑 활성화/비활성화 옵션 제공

#### 최적화된 경로 탐색 사용법

```javascript
// 기본 사용법
POST /tmsroute/optimized
{
  "command": "RequestRoute",
  "start": { "dx": 127.04498, "dy": 37.54253 },
  "reqid": "1600658759187",
  "routeoption": 512,
  "destpos": [
    { "dx": 127.045, "dy": 37.543, "nid": 1, "postalCode": "05029" },
    { "dx": 127.055, "dy": 37.553, "nid": 2, "postalCode": "06234" }
  ],
  "maxGroupSize": 10,           // 선택사항: 그룹당 최대 배송지 수 (기본값: 10)
  "usePostalCodeGrouping": true // 선택사항: 우편번호 그룹핑 사용 여부 (기본값: true)
}
```

### 5. 로그 관리 (향후 구현 예정)

- Proxy를 통한 모든 요청 및 응답에 대한 상세 로깅
- 효과적인 시스템 모니터링 및 문제 해결을 위한 로그 제공

### 6. 고가용성

- LBS 서버 장애에 대비한 이중화 관리 체계
- 자동 장애 조치를 통한 무중단 서비스 보장

### 7. 확장 가능한 아키텍처

- Proxy 서버에서 JavaScript를 통한 유연한 기능 확장
- 코어 LBS 시스템 영향 없이 빠른 비즈니스 로직 구현 및 기능 추가

### 8. CargoPlanning API 지원

- **Worker Pool 병렬 처리**: 기존 dispatch 서버의 병렬 처리 한계를 극복한 고성능 화물 계획 API
- **다양한 제약 조건**: 무게, 부피, 개수 기반 할당 및 균등 분배 지원
- **복합 제약 조건**: 여러 제약 조건을 우선순위에 따라 동시 적용 (limitedcombine, balancedcombine)
- **방문 순서 최적화**: 2opt, 3opt 알고리즘을 통한 TSP 최적화 지원

#### CargoPlanning API 엔드포인트
- `/cargoplanning` - 기본 화물 계획 (kmeans 군집화)
- `/cargoplanning/limitedweight` - 무게 제한 기반 할당
- `/cargoplanning/balancedweight` - 무게 균등 분배
- `/cargoplanning/limitedvolume` - 부피 제한 기반 할당
- `/cargoplanning/balancedvolume` - 부피 균등 분배
- `/cargoplanning/limitedcount` - 개수 제한 기반 할당
- `/cargoplanning/balancedcount` - 개수 균등 분배
- `/cargoplanning/limitedcombine?constraints=weight,volume,count` - 복합 제한
- `/cargoplanning/balancedcombine?constraints=weight,volume,count` - 복합 균등 분배
- `/cargoplanning/visitorder?mode=2opt` - 방문 순서 최적화

#### ⚠️ visitorder API 특별 사용법
**중요**: `/cargoplanning/visitorder` API는 2단계 프로세스로 사용해야 합니다:

1. **1단계**: 일반 cargoplanning API로 차량 할당 결과를 생성
2. **2단계**: 생성된 결과를 visitorder API로 방문순서 최적화

```bash
# 1단계: 차량 할당 결과 생성
curl -X POST http://localhost:15001/cargoplanning \
-H "Content-Type: application/json" \
-d '{...}' > result.json

# 2단계: 방문순서 최적화
curl -X POST "http://localhost:15001/cargoplanning/visitorder?mode=2opt" \
-H "Content-Type: application/json" \
-d @result.json
```

### 9. Swagger 지원

- Decorator를 활용한 빌드 시 swagger.yaml 파일 자동 생성
- OAS 3.0 표준을 준수하는 API 문서화

## 설정 및 사용법

1. Node.js 버전 설정:

   ```
   nvm use 20
   ```

2. 개발 서버 실행:

   ```
   npm run start:dev
   ```

3. 이미지 빌드 및 개발 환경 컨테이너 실행:

   - Jenkins를 통한 자동화된 빌드 및 배포
   - 빌드 Job URL: http://jenkins.logisteq.com:9191/view/LBS%20CORE/job/LBS%20PROXY%20%20Data%20Image%20Publish/

4. 개발 실행시 swagger.yaml 파일 생성 확인.
   업데이트 된 부분 있으면 https://gitlab.logisteq.com/infra/lbs-swagger-ui
   에서 swagger.yaml 파일을 업데이트를 실행합니다

## 테스트

최적화된 경로 탐색 기능의 테스트를 실행하려면:

```bash
npm test -- route.service.spec.ts
```

테스트는 다음 항목들을 검증합니다:

- 우편번호 기반 그룹핑 동작
- 기존 방식과 최적화된 방식의 결과 유사성
- 큰 그룹의 자동 분할
- 거리 계산 정확성

## 주의사항

- SSL 인증 설정은 별도로 진행해야 합니다.
- 로그 관리 기능은 현재 구현 중이며, 향후 업데이트 예정입니다.
- 최적화된 경로 탐색은 우편번호 정보가 있는 배송지에서 최적의 성능을 발휘합니다.

## 최근 업데이트

### 2025-07-25: CargoPlanning visitorder API 개선
- **payload 형식 변경**: visitorder API가 2단계 프로세스로 변경됨
- **입력 형식 수정**: 이제 클러스터링된 결과 JSON을 입력으로 받음
- **에러 방지**: "Initialization from result JSON failed" 에러 해결
- **문서화 개선**: Swagger 및 사용 가이드 업데이트

#### 주요 변경사항
- `VisitOrderRequestDto` 구조 변경
- 2단계 프로세스 필수화: 일반 cargoplanning → visitorder
- 입력 형식: `{result: [CargoPlanningResultDto[]]}` 사용
- TSP 최적화 알고리즘 지원: nearest, 2opt, 3opt

## 주소 서버

### Global Search

- 112.220.242.131:16002/version

### Global Router

- 112.220.242.131:16003/version
