# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LBS Proxy는 Location Based Service (LBS) 시스템을 위한 NestJS 기반 프록시 서버입니다. 이 서버는 로드 밸런싱, 장애 복구, 보안 강화를 제공하며, 특히 최적화된 경로 탐색 기능(우편번호 기반 그룹핑)을 포함합니다.

## Development Commands

### Essential Commands
```bash
# Node.js 버전 설정 (필수)
nvm use 20

# 개발 서버 실행
npm run start:dev

# 프로덕션 빌드
npm run build

# 프로덕션 서버 실행
npm run start:prod
```

### Testing Commands
```bash
# 모든 단위 테스트
npm run test

# 단위 테스트 (watch mode)
npm run test:watch

# 커버리지 포함 테스트
npm run test:cov

# 통합 테스트 (서버 실행 필요)
npm run test:integration

# 라우트 API 비교 테스트 (메인 통합 테스트)
npm run test:route-api-comparison

# E2E 테스트
npm run test:e2e

# 특정 테스트 파일 실행
npm test -- route.service.spec.ts
```

### Code Quality
```bash
# 린트 검사 및 자동 수정
npm run lint

# 코드 포맷팅
npm run format
```

### Docker
```bash
# 이미지 빌드
docker build -t lbs-proxy .

# 컨테이너 실행 (개발 환경)
docker-compose up -d
```

## Architecture

### Core Architecture Layers

**1. API Layer** (`src/api/`)
- `BaseApiService`: 로드 밸런싱, 서버 상태 관리, 장애 복구를 담당하는 기본 API 서비스
- 각 도메인별 API 서비스 (`route.api.service.ts`, `search.api.service.ts` 등)
- 환경변수를 통한 서버 목록 및 가중치 설정 (`{SERVICE}_SERVERS`, `{SERVICE}_WEIGHTS`)

**2. Service Layer** (각 도메인별 `src/*/`)
- **Route Service**: 경로 탐색 및 최적화 로직
  - `RouteService`: 기본 라우팅 API 호출
  - `RouteOptimizerService`: 우편번호 기반 그룹핑 및 최적화 로직
- **Search Service**: 주소 검색 기능
- **Dispatch Service**: 배차 관리 및 화물 계획
  - 기존 배차 API (K-means 클러스터링, 위치 기반 배차)
  - **신규 CargoPlanning API**: Worker pool 병렬 처리 지원
    - 무게/부피/개수 기반 제한 및 균등 분배
    - 복합 제약 조건 지원 (constraints 파라미터)
    - 방문 순서 최적화 (nearest, 2opt, 3opt 알고리즘)
- **Map/Meter Service**: 기타 지도 관련 서비스

**3. Common Layer** (`src/common/`)
- `TransactionContext`: 요청별 고유 트랜잭션 ID 관리
- Global Exception Filter 및 Response Interceptor
- Circuit Breaker 패턴 구현

**4. Middleware Layer** (`src/middleware/`)
- `LoggingInterceptor`: 요청/응답 로깅
- `CacheInterceptor`: 응답 캐싱
- `ResponseTransformInterceptor`: 응답 형식 변환

### Key Features

**로드 밸런싱 및 장애 복구**
- 서버별 가중치 및 호출 카운트 기반 라운드 로빈
- 30초마다 서버 헬스체크 (`/version` 엔드포인트)
- 실패 시 다른 서버로 자동 전환 (기본 3회 재시도)

**최적화된 경로 탐색**
- 우편번호 기반 배송지 그룹핑 (앞 4자리)
- 그룹별 최대 크기 제한 및 자동 분할
- 시작점으로부터 거리 기반 그룹 순서 최적화
- 레거시 API와 최적화 API 동시 지원

**Swagger 자동 생성**
- 서버 시작 시 `swagger.yaml` 파일 자동 생성
- OAS 3.0 표준 준수
- API 문서화용 데코레이터 활용

## Environment Variables

### Required Server Configuration
```bash
# 라우트 서버 설정 (필수)
ROUTE_SERVERS=server1:port,server2:port
ROUTE_WEIGHTS=1,2  # 선택사항

# 검색 서버 설정
SEARCH_SERVERS=server1:port,server2:port

# 배차 서버 설정 (CargoPlanning API 지원)
DISPATCH_SERVERS=server1:port,server2:port
DISPATCH_WEIGHTS=1,1  # 선택사항

# 기타 설정
API_TIMEOUT=180000  # 3분
CACHE_TTL=5000     # 5초
PORT=3000
```

### Docker Environment
```bash
NODE_ENV=production
TZ=Asia/Seoul
```

## Testing Guidelines

### Integration Testing (중요)
통합 테스트는 **실제 서버가 실행된 상태**에서만 동작합니다:

1. 서버 실행: `npm run start:dev`
2. 서버 확인: `curl http://localhost:15001/health`
3. 테스트 실행: `npm run test:route-api-comparison`

### Test Data Files
- `test/request1.json`: 5개 배송지 (기본 테스트)
- `test/request2.json`: 6개 배송지 (중간 테스트)
- `test/request3.json`: 400+개 배송지 (대용량 테스트)

### Test Results
결과는 `test/results/` 디렉토리에 저장:
- API 응답 파일: `request{N}-response{-old}.json`
- 비교 리포트: `request{N}-comparison-report.json`
- 성능 분석: `performance-analysis-report.json`

## Route Optimization Logic

### Algorithm Overview
1. **우편번호 그룹핑**: 배송지를 우편번호 앞 4자리로 그룹화
2. **그룹 크기 제한**: 각 그룹 최대 10개 배송지 (초과 시 분할)
3. **거리 기반 순서**: 시작점으로부터 가장 가까운 그룹부터 방문
4. **API 최적화**: 그룹별 개별 API 호출로 성능 향상

### Configuration Options
```typescript
{
  "maxGroupSize": 10,           // 그룹당 최대 배송지 수
  "usePostalCodeGrouping": true // 우편번호 그룹핑 사용 여부
}
```

## CargoPlanning API

### API 엔드포인트 목록
- `/cargoplanning` - 기본 화물 계획 (kmeans 군집화)
- `/cargoplanning/limitedweight` - 무게 제한 기반 할당
- `/cargoplanning/balancedweight` - 무게 균등 분배
- `/cargoplanning/limitedvolume` - 부피 제한 기반 할당
- `/cargoplanning/balancedvolume` - 부피 균등 분배
- `/cargoplanning/limitedcount` - 개수 제한 기반 할당
- `/cargoplanning/balancedcount` - 개수 균등 분배
- `/cargoplanning/limitedcombine?constraints=weight,volume,count` - 복합 제한 (우선순위 설정)
- `/cargoplanning/balancedcombine?constraints=weight,volume,count` - 복합 균등 분배
- `/cargoplanning/visitorder?mode=2opt` - 방문 순서 최적화

### Request Format
```typescript
{
  "vehicles": [
    {
      "vid": 1,
      "maxcount": 100,
      "dimensions": { "length": 3000, "width": 1800, "height": 1500, "unit": "mm" },
      "maxweight": { "value": 1000000, "unit": "g" },
      "location": { "dx": 127.04, "dy": 37.55 }
    }
  ],
  "dests": [
    {
      "nid": 101,
      "weight": { "value": 10, "unit": "kg" },
      "dimensions": { "length": 10, "width": 10, "height": 10, "unit": "cm" },
      "count": 1,
      "location": { "dx": 127.041, "dy": 37.551 }
    }
  ],
  "options": {
    "vehicleCount": 1,
    "capacity": 100
  }
}
```

### Query Parameters
- `constraints`: 제약 조건 우선순위 (예: `weight,volume,count`)
- `mode`: 방문 순서 알고리즘 (`nearest`, `2opt`, `3opt`)

### ⚠️ 중요: visitorder API 특별 사용법

`/cargoplanning/visitorder` API는 다른 cargoplanning API들과 **입력 형식이 다릅니다**!

#### 2단계 프로세스 필수

1. **1단계**: 일반 cargoplanning API로 차량 할당 결과를 생성
2. **2단계**: 생성된 결과를 visitorder API로 방문순서 최적화

```bash
# 1단계: 차량 할당 결과 생성
curl -X POST http://localhost:15001/cargoplanning \
-H "Content-Type: application/json" \
-d '{
  "vehicles": [
    {
      "vid": 1,
      "maxcount": 10,
      "dimensions": {"length": 3000, "width": 1800, "height": 1500, "unit": "mm"},
      "maxweight": {"value": 1000000, "unit": "g"},
      "location": {"dx": 127.04, "dy": 37.55}
    }
  ],
  "dests": [
    {"nid": 6001, "weight": {"value": 800, "unit": "g"}, "dimensions": {"length": 244, "width": 481, "height": 132, "unit": "mm"}, "location": {"dx": 127.041, "dy": 37.551}},
    {"nid": 6002, "weight": {"value": 750, "unit": "g"}, "dimensions": {"length": 200, "width": 400, "height": 130, "unit": "mm"}, "location": {"dx": 127.042, "dy": 37.552}},
    {"nid": 6003, "weight": {"value": 900, "unit": "g"}, "dimensions": {"length": 250, "width": 450, "height": 140, "unit": "mm"}, "location": {"dx": 127.043, "dy": 37.553}}
  ]
}' > cargoplanning_result.json

# 2단계: 방문순서 최적화
curl -X POST "http://localhost:15001/cargoplanning/visitorder?mode=2opt" \
-H "Content-Type: application/json" \
-d @cargoplanning_result.json
```

#### visitorder API 입력 데이터 형식

```json
{
  "result": [
    {
      "nvid": 1,                    // 차량 ID
      "location": [37.55, 127.04], // 차량 위치 [위도, 경도]
      "nids": [                    // 이미 할당된 배송지들
        {
          "nid": 6001,             // 배송지 ID
          "location": [37.551, 127.041] // 배송지 위치 [위도, 경도]
        },
        {
          "nid": 6002,
          "location": [37.552, 127.042]
        }
      ]
    }
  ]
}
```

#### 입력 형식 비교표

| API 유형 | 입력 데이터 형식 | 설명 |
|----------|------------------|------|
| **일반 cargoplanning APIs** | `{vehicles: [...], dests: [...]}` | 원시 입력 데이터 |
| **visitorder API** | `{result: [{nvid: ..., nids: [...]}]}` | 클러스터링된 결과 JSON |

#### 기술적 세부사항

- visitorder API는 내부적으로 `InitFromResultJson()` 함수를 사용
- 다른 API들은 `InitWithJSon()` 함수를 사용
- visitorder는 순수하게 방문순서 최적화만 수행 (`RunVisitOrderOnly()`)
- 2opt, 3opt 알고리즘을 통한 TSP(Traveling Salesman Problem) 최적화 적용

## Important File Locations

- **Main Application**: `src/main.ts`
- **Module Configuration**: `src/app.module.ts`
- **API Base Service**: `src/api/base.api.service.ts`
- **Route Optimizer**: `src/route/route-optimizer.service.ts`
- **DTO Definitions**: `src/types/*.dto.ts`
- **Docker Configuration**: `Dockerfile`, `docker-compose.yaml`
- **Build Configuration**: `nest-cli.json`, `tsconfig.json`

## Jenkins Integration

자동화된 빌드 및 배포:
- Build Job URL: http://jenkins.logisteq.com:9191/view/LBS%20CORE/job/LBS%20PROXY%20%20Data%20Image%20Publish/
- Swagger UI 업데이트: https://gitlab.logisteq.com/infra/lbs-swagger-ui

## Common Development Patterns

### Adding New API Services
1. `src/api/`에 새로운 API 서비스 생성 (BaseApiService 상속)
2. 환경변수에 서버 목록 설정 (`{SERVICE}_SERVERS`)
3. 도메인별 서비스에서 API 서비스 주입 및 사용
4. DTO 정의 및 Swagger 문서화

### Error Handling
- TransactionContext를 통한 요청별 고유 ID 추적
- 모든 API 호출에 재시도 로직 포함
- 글로벌 exception filter로 일관된 에러 응답

### Logging
- Winston 기반 구조화된 로깅
- 트랜잭션 ID 포함된 로그 추적
- 일별 로그 파일 로테이션