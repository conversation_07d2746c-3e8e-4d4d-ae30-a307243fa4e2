export class TileSize {
  /**
   * 타일 넓이 (px)
   * @example 256
   */
  width: number;

  /**
   * 타일 높이 (px)
   * @example 256
   */
  height: number;
}

export class PngAtPointItem {
  /**
   * 요청된 경도 위치 (타일 이미지 내 경도 위치, 0.0 ~ 1.0)
   * @example "0.489"
   */
  offsetX: string;

  /**
   * 요청된 위도 위치 (타일 이미지 내 위도 위치, 0.0 ~ 1.0)
   * @example "0.524"
   */
  offsetY: string;

  /**
   * base64로 인코딩된 png 타일 이미지
   * @example "data:image/png;base64,iVB...mCC"
   */
  base64Png: string;
}

export class PngAtPointResponseDto {
  /**
   * 결과 코드 ('00': success, '01': error)
   * @example "00"
   */
  resultCode: string;

  /**
   * 결과 메시지
   * @example "success"
   */
  resultMsg: string;

  /**
   * 맵 타일 사이즈
   */
  tileSize: TileSize;

  /**
   * 결과 목록 수 (0 또는 1)
   * @example 1
   */
  count: number;

  /**
   * 결과 목록
   */
  items: PngAtPointItem[];
}

export class PngInRangeItem {
  /**
   * 스크린 좌표 X (검색 범위가 스크린일 경우 타일의 위치)
   * @example -254
   */
  x: number;

  /**
   * 스크린 좌표 Y (검색 범위가 스크린일 경우 타일의 위치)
   * @example -7
   */
  y: number;

  /**
   * base64로 인코딩된 png 타일 이미지
   * @example "data:image/png;base64,iVB...mCC"
   */
  base64Png: string;
}

export class PngInRangeResponseDto {
  /**
   * 결과 코드 ('00': success, '01': error)
   * @example "00"
   */
  resultCode: string;

  /**
   * 결과 메시지
   * @example "success"
   */
  resultMsg: string;

  /**
   * 맵 타일 사이즈
   */
  tileSize: TileSize;

  /**
   * 결과 목록 수 (0...n)
   * @example 4
   */
  count: number;

  /**
   * 결과 목록
   */
  items: PngInRangeItem[];
}
