import { ApiProperty } from '@nestjs/swagger';

// Common Types
/**
 * 좌표를 나타내는 클래스
 */
export class Coordinate {
  /**
   * X 좌표 (경도, WGS84 좌표계)
   * @example 127.04498
   */
  @ApiProperty({ example: 127.04498, description: 'X 좌표 (경도, WGS84 좌표계)' })
  dx: number;

  /**
   * Y 좌표 (위도, WGS84 좌표계)
   * @example 37.54253
   */
  @ApiProperty({ example: 37.54253, description: 'Y 좌표 (위도, WGS84 좌표계)' })
  dy: number;
}

/**
 * 요청용 좌표를 나타내는 클래스
 * 기본 좌표에 추가 정보를 포함
 */
export class RequestCoordinate extends Coordinate {
  /**
   * 노드 ID
   * @example 10421614
   */
  @ApiProperty({ example: 10421614, description: '노드 ID', required: false })
  nid?: number;

  /**
   * 위치 타입
   * @example 'GENERAL'
   */
  @ApiProperty({ example: 'GENERAL', description: '위치 타입', required: false })
  type?: string;

  /**
   * 우편번호
   * @example '05029'
   */
  @ApiProperty({ example: '05029', description: '우편번호', required: false })
  postalCode?: string;
}

// Request DTOs
/**
 * TMS 경로 요청 DTO
 * 목적지 정렬 및 경로 탐색을 수행한다.
 */
export class TmsRouteRequestDto {
  /**
   * Route Core 동작요청을 구분함
   * @example 'RequestRoute'
   */
  command: string;

  /**
   * 출발 좌표
   */
  start: Coordinate;

  /**
   * 요청에 대한 고유번호
   * @example '1600658759187'
   */
  reqid: string;

  /**
   * 경로탐색 옵션
   * 차량종류, 경로순서 배정 기준, 경로 계산 시 적용 옵션을 설정
   * @example 512
   */
  routeoption: number;

  /**
   * 목적지 좌표 목록
   */
  destpos: RequestCoordinate[];

  /**
   * 차량 높이 (cm), 트럭 경로 시 필수
   * @example 0
   */
  height?: number;

  /**
   * 차량 무게 (kg), 트럭 경로 시 필수
   * @example 0
   */
  weight?: number;
}

/**
 * TMS 경로 목록 요청 DTO
 * 목적지 정렬 및 경로 탐색을 다중으로 수행한다.
 */
export class TmsRouteListRequestDto {
  /**
   * Route Core 동작요청을 구분함
   * @example 'RequestRouteList'
   */
  command: string;

  /**
   * 경로탐색 옵션
   * @example 512
   */
  routeoption: number;

  /**
   * 다중 경로탐색을 요청정보 개수
   * @example 3
   */
  routecount: number;

  /**
   * 다중 경로탐색 요청정보 배열
   */
  routelist: TmsRouteRequestDto[];
}

/**
 * TMS 온디맨드 경로 요청 DTO
 * 목적지 방문 순서가 정해진 상태에서 추가된 목적지에 대한 최적의 방문 순서 및 경로 탐색을 수행한다.
 */
export class TmsRouteOnDemandRequestDto extends TmsRouteRequestDto {
  /**
   * 새로운 방문지 좌표
   */
  new: Coordinate;

  /**
   * 새로운 방문지를 마지막에 방문
   * @example false
   */
  islast?: boolean;

  /**
   * 새로운 방문지를 첫 번째로 방문하지 않음
   * @example false
   */
  nofirst?: boolean;
}

/**
 * 경로 계산 요청 DTO
 * WGS84 경위도 좌표들을 이용하여 경로를 탐색한다.
 */
export class CalculateRouteRequestDto {
  /**
   * 차량 유형
   * @example 'truck'
   */
  @ApiProperty({ enum: ['car', 'truck', 'motorcycle'] })
  vehicleType: 'car' | 'truck' | 'motorcycle';

  /**
   * 회피 옵션
   * @example ['uturn', 'ferry']
   */
  avoidOptions: string[];

  /**
   * 차량 무게 (kg), 트럭 경로 시 필수
   * @example 2000
   */
  weight?: number;

  /**
   * 차량 높이 (cm), 트럭 경로 시 필수
   * @example 250
   */
  height?: number;

  /**
   * 정렬 옵션
   * @example 'enable'
   */
  @ApiProperty({ enum: ['enable', 'straight', 'manhattan', 'routeDistance', 'routeTime', 'disable'] })
  sort: 'enable' | 'straight' | 'manhattan' | 'routeDistance' | 'routeTime' | 'disable';

  /**
   * 출발 좌표
   */
  start: Coordinate;

  /**
   * 목적지 좌표 목록
   */
  destinations: Coordinate[];
}

// Response DTOs
/**
 * 경로 순서 정보
 */
export class RouteOrder {
  /**
   * 목적지 구분번호
   * @example '1'
   */
  @ApiProperty({ example: '1', description: '목적지 구분번호' })
  nid: string;

  /**
   * 방문 순서
   * @example 1
   */
  @ApiProperty({ example: 1, description: '방문 순서' })
  visit: number;

  /**
   * 도착예정시간(초)
   * @example 319
   */
  @ApiProperty({ example: 319, description: '도착예정시간(초)' })
  estimate: number;

  /**
   * 도달거리(미터)
   * @example 951
   */
  @ApiProperty({ example: 951, description: '도달거리(미터)' })
  dist: number;
}

/**
 * 경로 응답
 */
export class TmsRouteResponseDto {
  /**
   * 명령어
   * @example 'ResponseRoute'
   */
  @ApiProperty({ example: 'ResponseRoute', description: '명령어' })
  command: string;

  /**
   * 응답 ID
   * @example 1
   */
  @ApiProperty({ example: 1, description: '응답 ID' })
  resid: number;

  /**
   * 방문 순서 정보
   */
  @ApiProperty({ type: [RouteOrder], description: '방문 순서 정보' })
  order: RouteOrder[];

  /**
   * 경로 선형(좌표) 정보 - 실제 API 응답에 따라 유연하게 처리
   */
  @ApiProperty({
    description: '경로 선형(좌표) 정보',
  })
  pnts: any; // 실제 API 응답 형식에 맞게 유연하게 처리
}

/**
 * TMS 경로 목록 응답 DTO
 */
export class TmsRouteListResponseDto {
  /**
   * 명령어
   * @example 'ResponseRouteList'
   */
  command: string;

  /**
   * 경로 수
   * @example 3
   */
  routecount: number;

  /**
   * 경로 정보
   */
  routelist: TmsRouteResponseDto[];
}

/**
 * 경로 계산 응답 DTO
 */
export class CalculateRouteResponseDto {
  /**
   * 총 거리 (미터)
   * @example 10000
   */
  totalDistance: number;

  /**
   * 예상 소요 시간 (초)
   * @example 1200
   */
  estimatedTime: number;

  /**
   * 경로 좌표 목록
   */
  route: Coordinate[];
}

/**
 * 주행 범위 요청 DTO
 */
export class GetDriveRangeRequestDto {
  /**
   * X 좌표 (경도, WGS84 좌표계)
   * @example 127.04498
   */
  dx: number;

  /**
   * Y 좌표 (위도, WGS84 좌표계)
   * @example 37.54253
   */
  dy: number;

  /**
   * 방향 (도)
   * @example 90
   */
  dir: number;

  /**
   * 주행 거리 (미터)
   * @example 1000
   */
  meter: number;

  /**
   * 범위 (미터)
   * @example 100
   */
  range: number;
}

/**
 * 주행 범위 응답 DTO
 */
export class GetDriveRangeResponseDto {
  /**
   * 주행 범위를 나타내는 좌표 목록
   */
  pnts: Coordinate[];
}

/**
 * 우편번호 그룹 정보
 */
export class PostalCodeGroup {
  /**
   * 우편번호 (없는 경우 'NO_POSTAL_CODE')
   */
  @ApiProperty({ example: '06234', description: '우편번호 (없는 경우 NO_POSTAL_CODE)' })
  postalCode: string;

  /**
   * 해당 우편번호의 배송지 목록
   */
  @ApiProperty({ type: [RequestCoordinate], description: '해당 우편번호의 배송지 목록' })
  destinations: RequestCoordinate[];

  /**
   * 대표 좌표 (시작점과 가장 가까운 좌표)
   */
  @ApiProperty({ type: RequestCoordinate, description: '대표 좌표 (시작점과 가장 가까운 좌표)' })
  representative: RequestCoordinate;

  /**
   * 시작점으로부터의 거리
   */
  @ApiProperty({ example: 1500.5, description: '시작점으로부터의 거리(미터)' })
  distanceFromStart: number;
}

/**
 * 그룹별 경로 결과
 */
export class GroupRouteResult {
  /**
   * 우편번호
   */
  @ApiProperty({ example: '06234', description: '우편번호' })
  postalCode: string;

  /**
   * 그룹 내 경로 순서
   */
  @ApiProperty({ type: [RouteOrder], description: '그룹 내 경로 순서' })
  order: RouteOrder[];

  /**
   * 그룹 내 경로 좌표 - 실제 API 응답에 따라 유연하게 처리
   */
  @ApiProperty({
    description: '그룹 내 경로 좌표',
  })
  pnts: any; // 실제 API 응답 형식에 맞게 유연하게 처리

  /**
   * 그룹의 첫 번째 목적지
   */
  @ApiProperty({ type: RequestCoordinate, description: '그룹의 첫 번째 목적지' })
  firstDestination: RequestCoordinate;

  /**
   * 그룹의 마지막 목적지
   */
  @ApiProperty({ type: RequestCoordinate, description: '그룹의 마지막 목적지' })
  lastDestination: RequestCoordinate;
}

/**
 * 최적화된 TMS 경로 요청 DTO
 */
export class OptimizedTmsRouteRequestDto extends TmsRouteRequestDto {
  /**
   * 우편번호 그룹핑 사용 여부 (기본값: true)
   */
  @ApiProperty({ example: true, description: '우편번호 그룹핑 사용 여부', required: false, default: true })
  usePostalCodeGrouping?: boolean;
}
