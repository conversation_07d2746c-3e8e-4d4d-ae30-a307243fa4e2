/**
 * 좌표를 나타내는 클래스
 */
class LocationCoordinate {
  /**
   * 위도
   * @example 37.5109
   */
  latitude: number;

  /**
   * 경도
   * @example 126.94196
   */
  longitude: number;
}

/**
 * 거리 계산 응답 DTO
 */
export class CalculateMeterResponseDto {
  /**
   * 총 거리 (미터)
   * @example 10234
   */
  totalDistance: number;

  /**
   * 예상 소요 시간 (초)
   * @example 1234
   */
  estimatedTime: number;

  /**
   * 경로 좌표 목록
   */
  routeCoordinates: LocationCoordinate[];
}

/**
 * 주소 기반 거리 계산 응답 DTO
 */
export class CalculateMeterWithAddressesResponseDto extends CalculateMeterResponseDto {
  /**
   * 출발지 좌표
   */
  fromLocation: LocationCoordinate;

  /**
   * 도착지 좌표
   */
  toLocation: LocationCoordinate;
}

/**
 * 주소 기반 거리 계산 요청 DTO
 */
export class CalculateMeterWithAddressesDto {
  /**
   * 출발지 주소
   * @example '서울특별시 강남구 언주로30길 56'
   */
  fromAddress: string;

  /**
   * 도착지 주소
   * @example '서울특별시 성동구 왕십리로 58'
   */
  toAddress: string;

  /**
   * 거리 계산 옵션
   * @example 'route'
   */
  option: 'euclid' | 'manhattan' | 'route';
}

/**
 * 좌표 기반 거리 계산 요청 DTO
 */
export class CalculateMeterWithLocationsDto {
  /**
   * 출발지 좌표 [위도, 경도]
   * @example [37.5109, 126.94196]
   */
  fromLocation: number[];

  /**
   * 도착지 좌표 [위도, 경도]
   * @example [37.54578, 127.04694]
   */
  toLocation: number[];

  /**
   * 거리 계산 옵션
   * @example 'euclid'
   */
  option: 'euclid' | 'manhattan' | 'route';
}

/**
 * 경유지를 포함한 거리 계산 응답 DTO
 */
export class CalculateMeterWithViaLocationsResponseDto extends CalculateMeterResponseDto {
  /**
   * 경유지 거리 목록 (미터)
   * @example [1234, 2345, 3456]
   */
  viaDistances: number[];
}

/**
 * 경유지를 포함한 주소 기반 거리 계산 응답 DTO
 */
export class CalculateMeterWithViaAddressesResponseDto extends CalculateMeterWithViaLocationsResponseDto {
  /**
   * 출발지 좌표
   */
  fromLocation: LocationCoordinate;

  /**
   * 경유지 좌표 목록
   */
  viaLocations: LocationCoordinate[];

  /**
   * 도착지 좌표
   */
  toLocation: LocationCoordinate;
}

/**
 * 경유지를 포함한 주소 기반 거리 계산 요청 DTO
 */
export class CalculateMeterWithViaAddressesDto {
  /**
   * 출발지 주소
   * @example '서울특별시 성동구 왕십리로 58'
   */
  fromAddress: string;

  /**
   * 경유지 주소 목록
   * @example ['서울특별시 강남구 언주로30길 56', '서울특별시 강남구 언주로30길 57']
   */
  viaAddresses: string[];

  /**
   * 도착지 주소
   * @example '서울시 서초구 남부순환로 325길 9'
   */
  toAddress: string;

  /**
   * 거리 계산 옵션
   * @example 'euclid'
   */
  option: 'euclid' | 'manhattan' | 'route';
}

/**
 * 종합 거리 계산 요청 DTO
 */
export class CalculateMeterDto {
  /**
   * 출발지 주소
   * @example '서울특별시 강남구 언주로30길 56'
   */
  fromAddress: string;

  /**
   * 경유지 주소 목록
   * @example ['서울특별시 강남구 언주로30길 56', '서울특별시 강남구 언주로30길 57']
   */
  viaAddresses: string[];

  /**
   * 도착지 주소
   * @example '서울특별시 성동구 왕십리로 58'
   */
  toAddress: string;

  /**
   * 차량 유형
   * @example 'car'
   */
  vehicleType: 'car' | 'truck' | 'motorcycle';

  /**
   * 정렬 옵션
   * @example 'routeTime'
   */
  sort: 'routeTime' | 'routeDistance';
}

/**
 * 경유지를 포함한 좌표 기반 거리 계산 요청 DTO
 */
export class CalculateMeterWithViaLocationsDto {
  /**
   * 출발지 좌표 [위도, 경도]
   * @example [37.5109, 126.94196]
   */
  fromLocation: number[];

  /**
   * 경유지 좌표 목록 [[위도, 경도], ...]
   * @example [[37.61481, 126.94196], [37.64814, 127.023]]
   */
  viaLocations: number[][];

  /**
   * 도착지 좌표 [위도, 경도]
   * @example [37.50385, 127.04679]
   */
  toLocation: number[];

  /**
   * 거리 계산 옵션
   * @example 'route'
   */
  option: 'euclid' | 'manhattan' | 'route';
}

/**
 * 좌표 DTO
 */
export class LocationDto {
  /**
   * 좌표 [위도, 경도]
   * @example [37.5109, 126.94196]
   */
  coordinates: number[];
}

/**
 * 경유지를 포함한 좌표 기반 이동 시간 계산 요청 DTO
 */
export class CalculateTravelTimeWithViaLocationsDto {
  /**
   * 출발지 좌표
   */
  fromLocation: LocationDto;

  /**
   * 경유지 좌표 목록
   */
  viaLocations: LocationDto[];

  /**
   * 도착지 좌표
   */
  toLocation: LocationDto;
}

/**
 * 주소 기반 이동 시간 계산 요청 DTO
 */
export class CalculateTravelTimeWithAddressesDto {
  /**
   * 출발지 주소
   * @example '서울특별시 강남구 언주로30길 56'
   */
  fromAddress: string;

  /**
   * 도착지 주소
   * @example '서울특별시 성동구 왕십리로 58'
   */
  toAddress: string;
}

/**
 * 경유지를 포함한 주소 기반 이동 시간 계산 요청 DTO
 */
export class CalculateTravelTimeWithViaAddressesDto {
  /**
   * 출발지 주소
   * @example '서울특별시 강남구 언주로30길 56'
   */
  fromAddress: string;

  /**
   * 경유지 주소 목록
   * @example ['서울특별시 강남구 언주로30길 56', '서울특별시 강남구 언주로30길 57']
   */
  viaAddresses: string[];

  /**
   * 도착지 주소
   * @example '서울특별시 성동구 왕십리로 58'
   */
  toAddress: string;
}

/**
 * 위치 정보 클래스
 */
class LocationInfo {
  /**
   * 위치 고유 식별자
   * @example '0'
   */
  nid: string;

  /**
   * 위치 좌표 [위도, 경도]
   * @example [37.48865, 127.05392]
   */
  location: number[];

  /**
   * 방문 순서 (선택적)
   * @example 1
   */
  visit?: number;

  /**
   * 예상 도착 시간 (초) (선택적)
   * @example 2030
   */
  estimate?: number;

  /**
   * 이전 위치로부터의 거리 (미터) (선택적)
   * @example 8854
   */
  dist?: number;
}

/**
 * 주소 기반 이동 시간 계산 응답 DTO
 */
export class CalculateTravelTimeWithAddressesResponseDto {
  /**
   * 출발지, 경유지, 도착지를 포함한 위치 정보 배열
   */
  calculateTravelTimeWithAddresses: LocationInfo[];

  /**
   * 총 이동 시간 (분)
   * @example 34
   */
  travelTime: number;

  /**
   * 총 이동 거리 (미터)
   * @example 8854
   */
  distance: number;

  /**
   * 사용된 계산 옵션
   * @example 'route'
   */
  option: string;

  /**
   * 계산 결과 상태
   * @example 'pass'
   */
  result: string;
}
