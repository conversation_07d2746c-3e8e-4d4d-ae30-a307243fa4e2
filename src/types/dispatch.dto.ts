import { ApiProperty } from '@nestjs/swagger';

export class Location {
  /**
   * 위도
   * @example 37.54649
   */
  latitude: number;

  /**
   * 경도
   * @example 127.04329
   */
  longitude: number;
}

export class DestinationInfo {
  /**
   * 배송지 ID
   * @example '1'
   */
  destinationId: string;

  /**
   * 배송지 위치
   * @example [37.54649, 127.04329]
   */
  location: number[];
}

export class VehicleInfo {
  /**
   * 배송차량 ID
   * @example '1001'
   */
  vehicleId: string;

  /**
   * 배송차량 위치
   * @example [37.54649, 127.04329]
   */
  location: number[];

  /**
   * 현재 할당된 배송지 목록
   */
  destinations?: DestinationInfo[];
}

export class DispatchResponseDto {
  /**
   * 배차 결과
   * @example 'success'
   */
  result: string;

  /**
   * 배차된 차량 정보
   */
  vehicles: VehicleInfo[];
}

export class LocationBasedDispatchDto {
  /**
   * 차량 정보 배열
   */
  vehicles: VehicleInfo[];

  /**
   * 차량 대수
   * @example 2
   */
  vehicleCount?: number;

  /**
   * 배송지 정보 배열
   */
  destinations: DestinationInfo[];

  /**
   * 배차 타입
   * @example 'localizedDistribution'
   */
  @ApiProperty({ example: 'localizedDistribution', enum: ['localizedDistribution', 'equalDistribution'] })
  dispatchType: 'localizedDistribution' | 'equalDistribution';
}

export class SortCandidateWithEtaDto {
  /**
   * 차량 정보 배열
   */
  vehicles: VehicleInfo[];

  /**
   * 새 배송지 정보
   */
  newDestination: DestinationInfo;
}

export class SortCandidatesWithDistanceDto {
  /**
   * 출발지 좌표
   */
  fromLocation: Location;

  /**
   * 도착지 좌표
   */
  toLocation: Location;

  /**
   * 차량 정보 배열
   */
  vehicles: VehicleInfo[];

  /**
   * 새 배송지 정보
   */
  newDestination: DestinationInfo;
}

export class SortCandidatesWithRoutesDto {
  /**
   * 출발지 좌표
   */
  fromLocation: Location;

  /**
   * 도착지 좌표
   */
  toLocation: Location;

  /**
   * 차량 정보 배열
   */
  vehicles: VehicleInfo[];

  /**
   * 새 배송지 정보
   */
  newDestination: DestinationInfo;
}

export class SortResponseDto {
  /**
   * 정렬된 차량 ID 배열
   */
  sortedVehicleIds: string[];

  /**
   * 정렬 결과
   * @example 'success'
   */
  result: string;
}

export class SortTravelOrderDto {
  /**
   * 출발지 좌표
   */
  vehiclePosition: Location;

  /**
   * 배송지 정보 배열
   */
  destinations: DestinationInfo[];
}

export class SortVisitOrderDto {
  /**
   * 차량 유형
   */
  @ApiProperty({ enum: ['car', 'truck', 'motorcycle'] })
  vehicleType: 'car' | 'truck' | 'motorcycle';

  /**
   * 경로 유형
   */
  @ApiProperty({ enum: ['recommended', 'shortest', 'ecoroute'] })
  routeType: 'recommended' | 'shortest' | 'ecoroute';

  /**
   * 회피 옵션
   */
  avoidOptions: string[];

  /**
   * 차량 무게 (kg)
   * @example 2000
   */
  weight?: number;

  /**
   * 차량 높이 (cm)
   * @example 250
   */
  height?: number;

  /**
   * 출발지 좌표
   */
  start: Location;

  /**
   * 배송지 정보 배열
   */
  destinations: DestinationInfo[];
}

export class TravelOrderResponseDto {
  /**
   * 정렬된 방문 순서
   */
  sortedOrder: DestinationInfo[];

  /**
   * 총 이동 거리 (m)
   * @example 10000
   */
  totalDistance: number;

  /**
   * 예상 소요 시간 (초)
   * @example 3600
   */
  estimatedTime: number;
}

export class DestPosDto {
  /**
   * x 좌표
   */
  dx: number;

  /**
   * y 좌표
   */
  dy: number;

  /**
   * 목적지 ID
   */
  nid: number;
}

export class TmsMathRequestDto {
  /**
   * 명령어
   */
  command: string;

  /**
   * 차량 수
   */
  vehicnt: number;

  /**
   * 목적지 위치 정보
   */
  destpos: DestPosDto[];

  /**
   * 목표 수
   */
  targetcnt: number;
}

export class ClusterResultDto {
  /**
   * 차량 ID
   */
  nvid: number;

  /**
   * 할당된 목적지 ID 목록
   */
  nids: number[];
}

export class TmsMathResponseDto {
  /**
   * 명령어
   */
  command: string;

  /**
   * 클러스터링 결과
   */
  result: ClusterResultDto[];
}

// CargoPlanning API용 DTO 추가

export class DimensionsDto {
  /**
   * 길이
   * @example 3000
   */
  @ApiProperty({ example: 3000, description: '길이' })
  length: number;

  /**
   * 너비
   * @example 1800
   */
  @ApiProperty({ example: 1800, description: '너비' })
  width: number;

  /**
   * 높이
   * @example 1500
   */
  @ApiProperty({ example: 1500, description: '높이' })
  height: number;

  /**
   * 단위
   * @example 'mm'
   */
  @ApiProperty({ example: 'mm', description: '단위 (mm, cm, m)' })
  unit: string;
}

export class WeightDto {
  /**
   * 무게 값
   * @example 1000000
   */
  @ApiProperty({ example: 1000000, description: '무게 값' })
  value: number;

  /**
   * 무게 단위
   * @example 'g'
   */
  @ApiProperty({ example: 'g', description: '무게 단위 (g, kg, t)' })
  unit: string;
}

export class CargoLocationDto {
  /**
   * X 좌표 (경도)
   * @example 127.04
   */
  @ApiProperty({ example: 127.04, description: 'X 좌표 (경도)' })
  dx: number;

  /**
   * Y 좌표 (위도)
   * @example 37.55
   */
  @ApiProperty({ example: 37.55, description: 'Y 좌표 (위도)' })
  dy: number;
}

export class VehicleDto {
  /**
   * 차량 ID
   * @example 1
   */
  @ApiProperty({ example: 1, description: '차량 ID' })
  vid: number;

  /**
   * 최대 수용 개수
   * @example 100
   */
  @ApiProperty({ example: 100, description: '최대 수용 개수' })
  maxcount?: number;

  /**
   * 차량 크기
   */
  @ApiProperty({ type: DimensionsDto, description: '차량 크기' })
  dimensions?: DimensionsDto;

  /**
   * 최대 무게
   */
  @ApiProperty({ type: WeightDto, description: '최대 무게' })
  maxweight?: WeightDto;

  /**
   * 차량 위치
   */
  @ApiProperty({ type: CargoLocationDto, description: '차량 위치' })
  location: CargoLocationDto;
}

export class DestinationDto {
  /**
   * 목적지 ID
   * @example 101
   */
  @ApiProperty({ example: 101, description: '목적지 ID' })
  nid: number;

  /**
   * 화물 무게
   */
  @ApiProperty({ type: WeightDto, description: '화물 무게' })
  weight?: WeightDto;

  /**
   * 화물 크기
   */
  @ApiProperty({ type: DimensionsDto, description: '화물 크기' })
  dimensions?: DimensionsDto;

  /**
   * 화물 개수
   * @example 1
   */
  @ApiProperty({ example: 1, description: '화물 개수' })
  count?: number;

  /**
   * 목적지 위치
   */
  @ApiProperty({ type: CargoLocationDto, description: '목적지 위치' })
  location: CargoLocationDto;
}

export class CargoPlanningOptionsDto {
  /**
   * 차량 대수
   * @example 1
   */
  @ApiProperty({ example: 1, description: '차량 대수' })
  vehicleCount: number;

  /**
   * 용량
   * @example 100
   */
  @ApiProperty({ example: 100, description: '용량' })
  capacity: number;
}

export class CargoPlanningRequestDto {
  /**
   * 차량 목록
   */
  @ApiProperty({ type: [VehicleDto], description: '차량 목록' })
  vehicles: VehicleDto[];

  /**
   * 목적지 목록
   */
  @ApiProperty({ type: [DestinationDto], description: '목적지 목록' })
  dests: DestinationDto[];

  /**
   * 옵션
   */
  @ApiProperty({ type: CargoPlanningOptionsDto, description: '옵션' })
  options: CargoPlanningOptionsDto;
}

export class AssignedDestinationDto {
  /**
   * 목적지 위치 [위도, 경도]
   * @example [37.551, 127.041]
   */
  @ApiProperty({ example: [37.551, 127.041], description: '목적지 위치 [위도, 경도]' })
  location: number[];

  /**
   * 목적지 ID
   * @example 101
   */
  @ApiProperty({ example: 101, description: '목적지 ID' })
  nid: number;
}

export class CargoPlanningResultDto {
  /**
   * 차량 위치 [위도, 경도]
   * @example [37.55, 127.04]
   */
  @ApiProperty({ example: [37.55, 127.04], description: '차량 위치 [위도, 경도]' })
  location: number[];

  /**
   * 할당된 목적지 목록
   */
  @ApiProperty({ type: [AssignedDestinationDto], description: '할당된 목적지 목록' })
  nids: AssignedDestinationDto[];

  /**
   * 차량 ID
   * @example 1
   */
  @ApiProperty({ example: 1, description: '차량 ID' })
  nvid: number;
}

export class CargoPlanningResponseDto {
  /**
   * 명령어
   * @example 'cargoplanning'
   */
  @ApiProperty({ example: 'cargoplanning', description: '명령어' })
  command: string;

  /**
   * 결과 목록
   */
  @ApiProperty({ type: [CargoPlanningResultDto], description: '결과 목록' })
  result: CargoPlanningResultDto[];
}

export class VisitOrderRequestDto {
  /**
   * 카고플래닝 결과 (이미 클러스터링된 결과)
   */
  @ApiProperty({ type: [CargoPlanningResultDto], description: '이미 클러스터링된 결과 JSON' })
  result: CargoPlanningResultDto[];
}

export class VisitOrderResponseDto {
  /**
   * 명령어
   * @example 'cargoplanning'
   */
  @ApiProperty({ example: 'cargoplanning', description: '명령어' })
  command: string;

  /**
   * 방문 순서 최적화 결과
   */
  @ApiProperty({ type: [CargoPlanningResultDto], description: '방문 순서가 최적화된 결과 목록' })
  result: CargoPlanningResultDto[];
}
