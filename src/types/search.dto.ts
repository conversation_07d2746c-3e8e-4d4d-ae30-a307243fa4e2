/**
 * 위치 좌표를 나타내는 클래스
 */
class LocationCoordinate {
  /**
   * 위도
   * @example 37.5109
   */
  latitude: number;

  /**
   * 경도
   * @example 126.94196
   */
  longitude: number;
}

/**
 * 검색 주소 결과를 나타내는 클래스
 */
class SearchAddressResult {
  /**
   * 검색 결과 위치의 좌표
   */
  coordinates: LocationCoordinate;

  /**
   * 광역시/도 정보
   * @example '서울특별시'
   */
  adminarea: string;

  /**
   * 시/군/구 정보
   * @example '종로구'
   */
  subadminarea: string;

  /**
   * 읍/면/동 정보 또는 도로명
   * @example '봉익동'
   */
  locality: string;

  /**
   * 리 정보 (없을 경우 빈 문자열)
   * @example ''
   */
  sublocality: string;

  /**
   * 지번 정보 또는 건물 번호
   * @example '169'
   */
  subno: string;

  /**
   * 전체 주소 또는 장소명
   * @example '서울특별시 종로구 돈화문로6가길 2'
   */
  intersectinglocality: string;

  /**
   * 검색 요청 위치와의 거리 (미터)
   * @example 5561
   */
  distance: number;

  /**
   * 우편번호
   * @example '03138'
   */
  postalcode: string;

  /**
   * 도보 입구점 좌표
   */
  entrance: LocationCoordinate;

  /**
   * 차량 입구점 좌표
   */
  entrancevehicle: LocationCoordinate;
}

/**
 * 주소 검색 응답 DTO
 */
export class SearchAddressResponseDto {
  /**
   * 검색 결과 수
   * @example 1
   */
  count: number;

  /**
   * 주소 검색 결과 목록
   */
  searchaddress: SearchAddressResult[];
}

/**
 * 통합 검색 응답 DTO
 */
export class SearchResponseDto {
  /**
   * 검색 결과 수
   * @example 1
   */
  count: number;

  /**
   * 통합 검색 결과 목록
   */

  search: SearchAddressResult[];
}

/**
 * 좌표 검색 응답 DTO
 */
export class SearchCoordResponseDto {
  /**
   * 검색 결과 수
   * @example 1
   */
  count: number;

  /**
   * 좌표 검색 결과 목록
   */
  searchcoord: SearchAddressResult[];
}

/**
 * 입구점 검색 응답 DTO
 */
export class SearchEntranceResponseDto {
  /**
   * 입구점 X 좌표 (경도)
   * @example '127.026794'
   */
  xpos: string;

  /**
   * 입구점 Y 좌표 (위도)
   * @example '37.615124'
   */
  ypos: string;
}

// geocoding.dto.ts

export class GeocodingRequestDto {
  /**
   * 지오코딩 요청 갯수
   * @example 3
   */
  count: number;

  /**
   * 지오코딩 요청 주소
   * @example ["수원시 영통구 망포동 142-3", "서울특별시 송파구 오금로 310 중앙일보 강남공장", "서울특별시 서초구 효령로 182" ]
    ]
   */
  addr: string[];
}

/**
 * 지오코딩 응답 DTO
 */
export class GeocodingResponseDto {
  /**
   * 지오코딩 결과 수
   * @example 1
   */
  count: number;

  /**
   * 지오코딩 결과 목록
   */
  geocoding: SearchAddressResult[];
}

// 좌표 쌍을 나타내는 튜플 타입
type CoordinatePair = [number, number];

// 좌표 쌍의 배열을 나타내는 타입
type CoordinateArray = CoordinatePair[];

/**
 * 영역 검색 응답 DTO
 */
export class SearchAreaResponseDto {
  /**
   * 검색 결과 폴리곤 좌표 목록
   * @example [[37.5109, 126.94196], [37.511, 126.94197], [37.5111, 126.94198], [37.5109, 126.94196]]
   */
  areacoordinates: CoordinateArray;

  /**
   * 검색 결과 영역 유형
   * @example 'building'
   */
  areatype: string;

  /**
   * 검색 결과 수
   * @example '1'
   */
  count: string;
}

/**
 * POI 검색 결과의 개별 항목을 나타내는 클래스
 */
export class SearchPoiItemDto {
  /**
   * 검색 결과 위치의 WGS 84 좌표 [경도, 위도]
   * @example [127.04637, 37.54363]
   */
  coordinates: number[];

  /**
   * 광역시/도 정보
   * @example '서울특별시'
   */
  adminarea: string;

  /**
   * 시/군/구 정보
   * @example '성동구'
   */
  subadminarea: string;

  /**
   * 읍/면/동 정보 또는 도로명
   * @example '성수동1가'
   */
  locality: string;

  /**
   * 리 정보 (없을 경우 빈 문자열)
   * @example ''
   */
  sublocality: string;

  /**
   * 지번 또는 건물번호
   * @example '656-3'
   */
  subno: string;

  /**
   * 전체 주소
   * @example '서울특별시 성동구 왕십리로 58'
   */
  intersectinglocality: string;

  /**
   * 시설물 ID (없을 경우 빈 문자열)
   * @example ''
   */
  facilityid: string;

  /**
   * 전화번호 (없을 경우 빈 문자열)
   * @example ''
   */
  telephone: string;

  /**
   * 검색 요청 위치와의 거리 (미터)
   * @example 168
   */
  distance: number;

  /**
   * 우편번호
   * @example '04778'
   */
  postalcode: string;

  /**
   * 법정동코드
   * @example '1120011400'
   */
  bdongcode: string;

  /**
   * 건물 도보 입구점 WGS 84 좌표 [경도, 위도]
   * @example [127.046567, 37.543588]
   */
  entrance: number[];

  /**
   * 건물 차량 입구점 WGS 84 좌표 [경도, 위도] (없을 경우 [0, 0])
   * @example [0, 0]
   */
  entrancevehicle: number[];

  /**
   * 도로명
   * @example '왕십리로'
   */
  newRoadName: string;

  /**
   * 읍면동명
   * @example '성수동1가'
   */
  eupMyeonDong: string;

  /**
   * 지번 본번
   * @example '656'
   */
  bonBu: string;

  /**
   * 건물번호
   * @example '58'
   */
  buildingNum: string;

  /**
   * POI 명칭
   * @example '서울숲포휴'
   */
  pOIName: string;

  /**
   * 광역시/도 축약명
   * @example '서울'
   */
  adminAreaRdx: string;

  /**
   * 시/군/구 축약명
   * @example '성동'
   */
  subAdminAreaRdx: string;

  /**
   * 읍/면/동 축약명
   * @example '성수동1'
   */
  localityRdx: string;

  /**
   * 도로명 축약명
   * @example '왕십리로'
   */
  newRoadNameRdx: string;

  /**
   * 도로명 주소 축약명
   * @example '서울 성동 성수동1'
   */
  intersectingStreetLocalityRdx: string;

  /**
   * 도로명 주소 축약명2
   * @example '서울 성동 왕십리로'
   */
  intersectingStreetLocality2Rdx: string;
}

/**
 * POI 검색 응답 DTO
 */
export class SearchPoiResponseDto {
  /**
   * 검색 결과 수
   * @example 50
   */
  count: number;

  /**
   * POI 검색 결과 목록
   */
  searchpoi: SearchPoiItemDto[];
}

/**
 * 행정동 코드 검색 결과의 개별 항목을 나타내는 클래스
 */
export class DistrictCodeDto {
  /**
   * 행정동 코드
   * @example 1111000000
   */
  distcode: number;

  /**
   * 행정동 이름
   * @example '종로구'
   */
  distname: string;
}

/**
 * 행정동 코드 검색 응답 DTO
 */
export class SearchDistCodeResponseDto {
  /**
   * 행정동 코드 검색 결과 배열
   */
  distlist: DistrictCodeDto[];
}
