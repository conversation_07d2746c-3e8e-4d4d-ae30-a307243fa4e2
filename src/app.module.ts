import { <PERSON><PERSON><PERSON>, <PERSON>ope } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { ApiModule } from './api/api.module';
import { CommonModule } from './common/common.module';
import { DispatchModule } from './dispatch/dispatch.module';
import { MapModule } from './map/map.module';
import { MeterModule } from './meter/meter.module';
import { LoggingInterceptor } from './middleware/logging.interceptor';
import { RouteModule } from './route/route.module';
import { SearchModule } from './search/search.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CacheModule.register({
      isGlobal: true,
      ttl: +(process.env.CACHE_TTL || 5000),
      max: 100,
    }),
    SearchModule,
    RouteModule,
    DispatchModule,
    MeterModule,
    MapModule,
    CommonModule,
    ApiModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      scope: Scope.REQUEST,
      useClass: LoggingInterceptor,
    },
  ],
})
export class AppModule {}
