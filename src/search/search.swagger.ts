import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import {
  GeocodingResponseDto,
  SearchAddressResponseDto,
  SearchAreaResponseDto,
  SearchCoordResponseDto,
  SearchEntranceResponseDto,
  SearchResponseDto,
  SearchPoiResponseDto,
  SearchDistCodeResponseDto,
} from '../types/search.dto';

export function ApiSearchAddress() {
  return applyDecorators(
    ApiOperation({ summary: '주소검색' }),
    ApiQuery({
      name: 'addr',
      required: true,
      description: '주소(지번 주소 및 도로명 주소)를 이용하여 위치의 상세정보 검색을 수행',
      example: '서울시 종로구 돈화문로6가길 2',
    }),
    ApiQuery({ name: 'xpos', required: false, description: '검색요청 X 좌표', example: 127.04491 }),
    ApiQuery({ name: 'ypos', required: false, description: '검색요청 Y 좌표', example: 37.54265 }),
    ApiQuery({ name: 'sort', required: false, description: '검색결과 정렬을 위한 설정 값', example: 0 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchAddressResponseDto }),
  );
}

export function ApiSearch() {
  return applyDecorators(
    ApiOperation({ summary: '통합검색' }),
    ApiQuery({ name: 'keyword', required: true, description: '통합검색을 위한 검색어', example: '서울숲' }),
    ApiQuery({ name: 'xpos', required: false, description: '검색요청 X 좌표', example: 127.04491 }),
    ApiQuery({ name: 'ypos', required: false, description: '검색요청 Y 좌표', example: 37.54265 }),
    ApiQuery({ name: 'sort', required: false, description: '검색결과 정렬을 위한 설정 값', example: 0 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchResponseDto }),
  );
}

export function ApiExtSearch() {
  return applyDecorators(
    ApiOperation({ summary: '확장된 통합검색' }),
    ApiQuery({ name: 'keyword', required: true, description: '통합검색을 위한 검색어', example: '서울숲' }),
    ApiQuery({ name: 'xpos', required: false, description: '검색요청 X 좌표', example: 127.04491 }),
    ApiQuery({ name: 'ypos', required: false, description: '검색요청 Y 좌표', example: 37.54265 }),
    ApiQuery({ name: 'sort', required: false, description: '검색결과 정렬을 위한 설정 값', example: 0 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchResponseDto }),
  );
}

export function ApiSearchCoord() {
  return applyDecorators(
    ApiOperation({ summary: '좌표검색' }),
    ApiQuery({ name: 'xpos', required: true, description: '검색요청 X 좌표', example: 127.0268 }),
    ApiQuery({ name: 'ypos', required: true, description: '검색요청 Y 좌표', example: 37.61516 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchCoordResponseDto }),
  );
}

export function ApiExtSearchCoord() {
  return applyDecorators(
    ApiOperation({ summary: '확장된 좌표검색' }),
    ApiQuery({ name: 'xpos', required: true, description: '검색요청 X 좌표', example: 127.0268 }),
    ApiQuery({ name: 'ypos', required: true, description: '검색요청 Y 좌표', example: 37.61516 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchCoordResponseDto }),
  );
}

export function ApiSearchEntrance() {
  return applyDecorators(
    ApiOperation({ summary: '입구점검색' }),
    ApiQuery({ name: 'xpos', required: true, description: '검색요청 X 좌표', example: 127.0268 }),
    ApiQuery({ name: 'ypos', required: true, description: '검색요청 Y 좌표', example: 37.61516 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchEntranceResponseDto }),
  );
}

export function ApiGeocoding() {
  return applyDecorators(
    ApiOperation({ summary: '지오코딩' }),
    ApiResponse({ status: 200, description: 'Successful response', type: GeocodingResponseDto }),
  );
}

export function ApiSearchArea() {
  return applyDecorators(
    ApiOperation({ summary: '지오펜스' }),
    ApiQuery({ name: 'xpos', required: true, description: '검색요청 X 좌표', example: 127.1234937 }),
    ApiQuery({ name: 'ypos', required: true, description: '검색요청 Y 좌표', example: 37.351992 }),
    ApiQuery({ name: 'expand', required: false, description: '확장 거리 정보(미터)', example: 100 }),
    ApiQuery({ name: 'type', required: false, description: '영역 검색 종류', example: 0 }),
    ApiResponse({ status: 200, description: 'Successful response', type: SearchAreaResponseDto }),
  );
}

export function ApiSearchPoi() {
  return applyDecorators(
    ApiOperation({
      summary: 'POI 검색',
      description: '건물명이나 상호명 등의 명칭으로 위치 검색을 수행하며, 초성 입력으로도 검색이 가능합니다.',
    }),
    ApiQuery({
      name: 'keyword',
      required: true,
      description: `POI 검색을 위한 검색어
      - 건물명칭 (예: 서울숲포휴)
      - 상호명 (예: 엔지스테크널러지)
      - 초성 (예: ㅅㅇㅅ)`,
      example: '서울숲',
    }),
    ApiQuery({
      name: 'xpos',
      required: false,
      description: 'WGS 84 X좌표(longitude). 검색 기준점으로 사용되며, 거리 계산 및 정렬에 활용됩니다.',
      example: 127.04491,
    }),
    ApiQuery({
      name: 'ypos',
      required: false,
      description: 'WGS 84 Y좌표(latitude). 검색 기준점으로 사용되며, 거리 계산 및 정렬에 활용됩니다.',
      example: 37.54265,
    }),
    ApiQuery({
      name: 'sort',
      required: false,
      description: `검색결과 정렬 옵션:
      - 0: 정확도 순 (기본값)
      - 1: 거리 순 (좌표 입력 시)
      - 2: 명칭 순`,
      example: 0,
    }),
    ApiResponse({
      status: 200,
      description: '검색 성공',
      type: SearchPoiResponseDto,
    }),
    ApiResponse({
      status: 204,
      description: '검색 결과 없음',
    }),
    ApiResponse({
      status: 400,
      description: '잘못된 요청 파라미터',
    }),
    ApiResponse({
      status: 404,
      description: '검색 서버 연결 오류',
    }),
  );
}

export function ApiSearchDistCode() {
  return applyDecorators(
    ApiOperation({
      summary: '행정동코드로 검색',
      description: '행정동코드(distcode)와 선택적인 키워드를 사용하여 위치 정보를 검색합니다.',
    }),
    ApiQuery({
      name: 'distcode',
      required: true,
      type: 'number',
      description: '검색할 행정동코드 (10자리 숫자)',
      example: 1100000000,
    }),
    ApiQuery({
      name: 'keyword',
      required: false,
      type: 'string',
      description: '선택적 검색 키워드',
      example: '카페',
    }),
    ApiResponse({
      status: 200,
      description: '검색 성공',
      type: SearchDistCodeResponseDto,
    }),
    ApiResponse({
      status: 400,
      description: '잘못된 요청 파라미터 (예: distcode 누락 또는 형식 오류)',
    }),
    ApiResponse({
      status: 500,
      description: '서버 내부 오류',
    }),
  );
}
