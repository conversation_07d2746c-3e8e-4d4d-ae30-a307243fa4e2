import { Injectable } from '@nestjs/common';
import { SearchApiService } from '../api/search.api.service';
import {
  GeocodingRequestDto,
  GeocodingResponseDto,
  SearchAddressResponseDto,
  SearchAreaResponseDto,
  SearchCoordResponseDto,
  SearchDistCodeResponseDto,
  SearchEntranceResponseDto,
  SearchResponseDto,
} from '../types/search.dto';

@Injectable()
export class SearchService {
  constructor(private searchApiService: SearchApiService) {}

  getVersion() {
    return this.searchApiService.callApi<string>('/version', 'GET');
  }

  searchAddress(addr: string, xpos: number, ypos: number, sort: number) {
    return this.searchApiService.callApi<SearchAddressResponseDto>('/searchaddress', 'GET', { addr, xpos, ypos, sort });
  }

  search(keyword: string, xpos: number, ypos: number, sort: number) {
    return this.searchApiService.callApi<SearchResponseDto>('/search', 'GET', {
      keyword,
      xpos,
      ypos,
      sort,
    });
  }

  extSearch(keyword: string, xpos: number, ypos: number, sort: number) {
    return this.searchApiService.callApi<SearchResponseDto>('/extsearch', 'GET', { keyword, xpos, ypos, sort });
  }

  searchCoord(xpos: number, ypos: number) {
    return this.searchApiService.callApi<SearchCoordResponseDto>('/searchcoord', 'GET', { xpos, ypos });
  }

  extSearchCoord(xpos: number, ypos: number) {
    return this.searchApiService.callApi<SearchCoordResponseDto>('/extsearchcoord', 'GET', { xpos, ypos });
  }

  searchEntrance(xpos: number, ypos: number) {
    return this.searchApiService.callApi<SearchEntranceResponseDto>('/searchentrance', 'GET', { xpos, ypos });
  }

  geocoding(body: GeocodingRequestDto) {
    return this.searchApiService.callApi<GeocodingResponseDto>('/geocoding', 'POST', undefined, body);
  }

  searchArea(xpos: number, ypos: number, expand: number, type: number) {
    return this.searchApiService.callApi<SearchAreaResponseDto>('/searcharea', 'GET', { xpos, ypos, expand, type });
  }

  searchPoi(keyword: string, xpos?: number, ypos?: number, sort?: number) {
    return this.searchApiService.callApi<SearchResponseDto>('/searchpoi', 'GET', {
      keyword,
      xpos,
      ypos,
      sort,
    });
  }

  searchDistCode(distcode: number, keyword?: string) {
    return this.searchApiService.callApi<SearchDistCodeResponseDto>('/searchdistcode', 'GET', { distcode, keyword });
  }
}
