import { Body, Controller, Get, Logger, Post, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CacheInterceptor } from '../middleware/cache.interceptor';
import { GeocodingRequestDto } from '../types/search.dto';
import { SearchService } from './search.service';

import {
  ApiExtSearch,
  ApiExtSearchCoord,
  ApiGeocoding,
  ApiSearch,
  ApiSearchAddress,
  ApiSearchArea,
  ApiSearchCoord,
  ApiSearchEntrance,
  ApiSearchPoi,
  ApiSearchDistCode,
} from './search.swagger';

@ApiTags('Search APIs')
@Controller()
export class SearchController {
  private readonly logger = new Logger(SearchController.name);

  constructor(private readonly searchService: SearchService) {}

  @Get('search/version')
  getVersion() {
    return this.searchService.getVersion();
  }

  @UseInterceptors(CacheInterceptor)
  @ApiSearchAddress()
  @Get('searchaddress')
  searchAddress(
    @Query('addr') addr: string,
    @Query('xpos') xpos: number,
    @Query('ypos') ypos: number,
    @Query('sort') sort: number,
  ) {
    return this.searchService.searchAddress(addr, xpos, ypos, sort);
  }

  @ApiSearch()
  @UseInterceptors(CacheInterceptor)
  @Get('search')
  search(
    @Query('keyword') keyword: string,
    @Query('xpos') xpos: number,
    @Query('ypos') ypos: number,
    @Query('sort') sort: number,
  ) {
    return this.searchService.search(keyword, xpos, ypos, sort);
  }

  @ApiExtSearch()
  @UseInterceptors(CacheInterceptor)
  @Get('extsearch')
  extSearch(
    @Query('keyword') keyword: string,
    @Query('xpos') xpos: number,
    @Query('ypos') ypos: number,
    @Query('sort') sort: number,
  ) {
    return this.searchService.extSearch(keyword, xpos, ypos, sort);
  }

  @ApiSearchCoord()
  @Get('searchcoord')
  searchCoord(@Query('xpos') xpos: number, @Query('ypos') ypos: number) {
    return this.searchService.searchCoord(xpos, ypos);
  }

  @ApiExtSearchCoord()
  @Get('extsearchcoord')
  extSearchCoord(@Query('xpos') xpos: number, @Query('ypos') ypos: number) {
    return this.searchService.extSearchCoord(xpos, ypos);
  }

  @ApiSearchEntrance()
  @Get('searchentrance')
  searchEntrance(@Query('xpos') xpos: number, @Query('ypos') ypos: number) {
    return this.searchService.searchEntrance(xpos, ypos);
  }

  @ApiGeocoding()
  @Post('geocoding')
  geocoding(@Body() body: GeocodingRequestDto) {
    return this.searchService.geocoding(body);
  }

  @ApiSearchArea()
  @Get('searcharea')
  searchArea(
    @Query('xpos') xpos: number,
    @Query('ypos') ypos: number,
    @Query('expand') expand: number,
    @Query('type') type: number,
  ) {
    return this.searchService.searchArea(xpos, ypos, expand, type);
  }

  @ApiSearchPoi()
  @Get('searchpoi')
  searchPoi(
    @Query('keyword') keyword: string,
    @Query('xpos') xpos?: number,
    @Query('ypos') ypos?: number,
    @Query('sort') sort?: number,
  ) {
    return this.searchService.searchPoi(keyword, xpos, ypos, sort);
  }

  @ApiSearchDistCode()
  @Get('searchdistcode')
  searchDistCode(@Query('distcode') distcode: number, @Query('keyword') keyword?: string) {
    return this.searchService.searchDistCode(distcode, keyword);
  }
}
