import * as winston from 'winston';
import { utilities as nestWinstonModuleUtilities, WinstonModule } from 'nest-winston';

export const setupWinston = () => {
  return WinstonModule.createLogger({
    transports: [
      new winston.transports.Console({
        level: 'silly',
        format: winston.format.combine(
          winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
          winston.format.ms(),
          nestWinstonModuleUtilities.format.nestLike('admin-web'),
        ),
      }),
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      new (require('winston-daily-rotate-file'))({
        level: process.env.NODE_ENV === 'production' ? 'info' : 'silly',
        format: winston.format.combine(
          winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
          winston.format.ms(),
          nestWinstonModuleUtilities.format.nestLike('aloa-admin', {
            prettyPrint: true,
          }),
        ),
        filename: '/logs/lbs-proxy/lbs-proxy-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        maxSize: '20m',
        maxFiles: '14d',
      }),
    ],
  });
};
