import { Injectable } from '@nestjs/common';
import { DispatchApiService } from '../api/dispatch.api.service';
import {
  LocationBasedDispatchDto,
  SortCandidateWithEtaDto,
  SortCandidatesWithDistanceDto,
  SortCandidatesWithRoutesDto,
  SortTravelOrderDto,
  SortVisitOrderDto,
  TmsMathRequestDto,
  TmsMathResponseDto,
  CargoPlanningRequestDto,
  CargoPlanningResponseDto,
  VisitOrderRequestDto,
} from '../types/dispatch.dto';

@Injectable()
export class DispatchService {
  constructor(private readonly dispatchApiService: DispatchApiService) {}

  getVersion() {
    return this.dispatchApiService.callApi<string>('/version', 'GET');
  }

  locationBasedDispatch(body: LocationBasedDispatchDto) {
    return this.dispatchApiService.callApi<any>('/locationbaseddispatch', 'POST', undefined, body);
  }

  sortCandidateWithEta(body: SortCandidateWithEtaDto) {
    return this.dispatchApiService.callApi<any>('/sortcandidatewitheta', 'POST', undefined, body);
  }

  sortCandidatesWithRoutes(body: SortCandidatesWithRoutesDto) {
    return this.dispatchApiService.callApi<any>('/sortcandidateswithroutes', 'POST', undefined, body);
  }

  sortCandidatesWithDistance(body: SortCandidatesWithDistanceDto) {
    return this.dispatchApiService.callApi<any>('/sortcandidateswithdistance', 'POST', undefined, body);
  }

  sortTravelOrder(body: SortTravelOrderDto) {
    return this.dispatchApiService.callApi<any>('/sorttravelorder', 'POST', undefined, body);
  }

  sortVisitOrder(body: SortVisitOrderDto) {
    return this.dispatchApiService.callApi<any>('/sortvisitorder', 'POST', undefined, body);
  }

  tmsMath(body: TmsMathRequestDto) {
    return this.dispatchApiService.callApi<TmsMathResponseDto>('/tmsmath', 'POST', undefined, body);
  }

  // CargoPlanning API 메서드들

  /**
   * 기본 화물 계획 (kmeans 군집화)
   */
  async cargoPlanning(body: CargoPlanningRequestDto) {
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>('/cargoplanning', 'POST', undefined, body);
    
  }

  /**
   * 무게 제한 기반 화물 계획
   */
  async cargoPlanningLimitedWeight(body: CargoPlanningRequestDto): Promise<CargoPlanningResponseDto> {
    const response = await this.dispatchApiService.callApi<CargoPlanningResponseDto>('/cargoplanning/limitedweight', 'POST', undefined, body);
    return response.data;
  }

  /**
   * 무게 균등 분배 화물 계획
   */
  cargoPlanningBalancedWeight(body: CargoPlanningRequestDto) {
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(
      '/cargoplanning/balancedweight',
      'POST',
      undefined,
      body,
    );
  }

  /**
   * 부피 제한 기반 화물 계획
   */
  cargoPlanningLimitedVolume(body: CargoPlanningRequestDto) {
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(
      '/cargoplanning/limitedvolume',
      'POST',
      undefined,
      body,
    );
  }

  /**
   * 부피 균등 분배 화물 계획
   */
  cargoPlanningBalancedVolume(body: CargoPlanningRequestDto) {
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(
      '/cargoplanning/balancedvolume',
      'POST',
      undefined,
      body,
    );
  }

  /**
   * 개수 제한 기반 화물 계획
   */
  cargoPlanningLimitedCount(body: CargoPlanningRequestDto) {
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(
      '/cargoplanning/limitedcount',
      'POST',
      undefined,
      body,
    );
  }

  /**
   * 개수 균등 분배 화물 계획
   */
  cargoPlanningBalancedCount(body: CargoPlanningRequestDto) {
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(
      '/cargoplanning/balancedcount',
      'POST',
      undefined,
      body,
    );
  }

  /**
   * 복합 제한 기반 화물 계획
   */
  cargoPlanningLimitedCombine(body: CargoPlanningRequestDto, constraints?: string) {
    const endpoint = constraints
      ? `/cargoplanning/limitedcombine?constraints=${constraints}`
      : '/cargoplanning/limitedcombine';
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(endpoint, 'POST', undefined, body);
  }

  /**
   * 복합 균등 분배 화물 계획
   */
  cargoPlanningBalancedCombine(body: CargoPlanningRequestDto, constraints?: string) {
    const endpoint = constraints
      ? `/cargoplanning/balancedcombine?constraints=${constraints}`
      : '/cargoplanning/balancedcombine';
    return this.dispatchApiService.callApi<CargoPlanningResponseDto>(endpoint, 'POST', undefined, body);
  }

  /**
   * 방문 순서 결정
   */
  async cargoPlanningVisitOrder(body: VisitOrderRequestDto, mode?: string): Promise<CargoPlanningResponseDto> {
    const endpoint = mode ? `/cargoplanning/visitorder?mode=${mode}` : '/cargoplanning/visitorder';
    const response = await this.dispatchApiService.callApi<CargoPlanningResponseDto>(endpoint, 'POST', undefined, body);
    return response.data;
  }
}
