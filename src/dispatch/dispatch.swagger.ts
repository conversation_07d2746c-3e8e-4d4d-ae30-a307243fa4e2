import { applyDecorators } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  LocationBasedDispatchDto,
  SortCandidateWithEtaDto,
  SortCandidatesWithDistanceDto,
  SortCandidatesWithRoutesDto,
  SortTravelOrderDto,
  SortVisitOrderDto,
  CargoPlanningRequestDto,
  CargoPlanningResponseDto,
  VisitOrderRequestDto,
} from '../types/dispatch.dto';

export function ApiLocationBasedDispatch() {
  return applyDecorators(
    ApiOperation({
      summary: 'Location based dispatch',
      description:
        '차량의 출발 위치(물류센터)와 화물의 배송지를 기반으로 배송에 소요되는 예상 시간이 일정하도록 군집화한다.',
    }),
    ApiBody({
      type: LocationBasedDispatchDto,
      examples: {
        locationBasedDispatchExample: {
          summary: 'Sample',
          value: {
            vehicles: [
              {
                vehicleId: '1001',
                location: [37.54649, 127.04329],
              },
              {
                vehicleId: '1002',
                location: [37.55514, 127.13197],
              },
            ],
            dispatchType: '1',
            destinations: [
              {
                destinationId: '1',
                location: [37.54649, 127.04329],
              },
              {
                destinationId: '2',
                location: [37.54578, 127.04246],
              },
              {
                destinationId: '3',
                location: [37.53837, 127.04608],
              },
              {
                destinationId: '4',
                location: [37.54655, 127.04162],
              },
              {
                destinationId: '5',
                location: [37.53837, 127.04608],
              },
              {
                destinationId: '6',
                location: [37.54655, 127.04162],
              },
              {
                destinationId: '7',
                location: [37.54578, 126.94347],
              },
              {
                destinationId: '8',
                location: [37.54578, 127.04694],
              },
              {
                destinationId: '9',
                location: [37.49931, 127.09096],
              },
              {
                destinationId: '10',
                location: [37.60143, 127.0128],
              },
              {
                destinationId: '11',
                location: [37.48415, 126.94347],
              },
              {
                destinationId: '12',
                location: [37.48415, 126.94347],
              },
              {
                destinationId: '13',
                location: [37.48415, 126.94347],
              },
              {
                destinationId: '14',
                location: [37.57447, 126.90174],
              },
              {
                destinationId: '15',
                location: [37.57447, 126.90274],
              },
              {
                destinationId: '16',
                location: [37.5265, 126.87448],
              },
              {
                destinationId: '17',
                location: [37.62874, 127.01978],
              },
              {
                destinationId: '18',
                location: [37.55514, 127.13197],
              },
              {
                destinationId: '19',
                location: [37.54936, 126.87012],
              },
              {
                destinationId: '20',
                location: [37.50255, 127.00936],
              },
              {
                destinationId: '21',
                location: [37.51012, 127.07128],
              },
              {
                destinationId: '22',
                location: [37.55805, 126.86601],
              },
            ],
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiSortCandidateWithEta() {
  return applyDecorators(
    ApiOperation({
      summary: 'Sort candidate with ETA',
      description:
        '배송중 새로운 배송지가 추가되었을 때 배송 차량들의 경로 ETA를 기반으로 가장 적합한 배송 차량에 배송지를 추가한 후 할당된 배송 차량 정보와 배송지 목록을 업데이트하여 반환한다.',
    }),
    ApiBody({
      type: SortCandidateWithEtaDto,
      examples: {
        sortCandidateWithEtaExample: {
          summary: 'Sample',
          value: {
            vehicles: [
              {
                vehicleId: '1001',
                vehiclePosition: [37.54649, 127.04329],
                destinations: [
                  {
                    destinationId: '1',
                    location: [37.54649, 127.04329],
                  },
                  {
                    destinationId: '2',
                    location: [37.54578, 127.04246],
                  },
                  {
                    destinationId: '3',
                    location: [37.53837, 127.04608],
                  },
                  {
                    destinationId: '4',
                    location: [37.54655, 127.04162],
                  },
                  {
                    destinationId: '5',
                    location: [37.53837, 127.04608],
                  },
                  {
                    destinationId: '6',
                    location: [37.54655, 127.04162],
                  },
                ],
              },
              {
                vehicleId: '1002',
                vehiclePosition: [37.55514, 127.13197],
                destinations: [
                  {
                    destinationId: '7',
                    location: [37.54578, 126.94347],
                  },
                  {
                    destinationId: '8',
                    location: [37.54578, 127.04694],
                  },
                  {
                    destinationId: '9',
                    location: [37.49931, 127.09096],
                  },
                  {
                    destinationId: '10',
                    location: [37.60143, 127.0128],
                  },
                  {
                    destinationId: '11',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '12',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '13',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '14',
                    location: [37.57447, 126.90174],
                  },
                  {
                    destinationId: '15',
                    location: [37.57447, 126.90274],
                  },
                  {
                    destinationId: '16',
                    location: [37.5265, 126.87448],
                  },
                  {
                    destinationId: '17',
                    location: [37.62874, 127.01978],
                  },
                  {
                    destinationId: '18',
                    location: [37.55514, 127.13197],
                  },
                  {
                    destinationId: '19',
                    location: [37.54936, 126.87012],
                  },
                  {
                    destinationId: '20',
                    location: [37.50255, 127.00936],
                  },
                  {
                    destinationId: '21',
                    location: [37.51012, 127.07128],
                  },
                ],
              },
            ],
            newDestination: {
              destinationId: 3000,
              location: [37.55805, 126.86601],
            },
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiSortCandidatesWithRoutes() {
  return applyDecorators(
    ApiOperation({
      summary: 'Sort candidates with routes',
      description:
        '두지점 (출발지, 도착지) 좌표를 입력 받아 운행하는 차량들의 경로를 기반으로 최적 운송 차량을 결정해 준다.',
    }),
    ApiBody({
      type: SortCandidatesWithRoutesDto,
      examples: {
        sample: {
          summary: 'Sample',
          value: {
            fromLocation: [37.54649, 127.04329],
            toLocation: [37.54659, 127.04229],
            vehicles: [
              {
                vehicleId: '1001',
                vehiclePosition: [37.54649, 127.04329],
                destinations: [
                  {
                    destinationId: '1',
                    location: [37.54649, 127.04329],
                  },
                  {
                    destinationId: '2',
                    location: [37.54578, 127.04246],
                  },
                  {
                    destinationId: '3',
                    location: [37.53837, 127.04608],
                  },
                  {
                    destinationId: '4',
                    location: [37.54655, 127.04162],
                  },
                  {
                    destinationId: '5',
                    location: [37.53837, 127.04608],
                  },
                  {
                    destinationId: '6',
                    location: [37.54655, 127.04162],
                  },
                ],
              },
              {
                vehicleId: '1002',
                vehiclePosition: [37.54649, 127.04329],
                destinations: [
                  {
                    destinationId: '7',
                    location: [37.54578, 126.94347],
                  },
                  {
                    destinationId: '8',
                    location: [37.54578, 127.04694],
                  },
                  {
                    destinationId: '9',
                    location: [37.49931, 127.09096],
                  },
                  {
                    destinationId: '10',
                    location: [37.60143, 127.0128],
                  },
                  {
                    destinationId: '11',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '12',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '13',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '14',
                    location: [37.57447, 126.90174],
                  },
                  {
                    destinationId: '15',
                    location: [37.57447, 126.90274],
                  },
                  {
                    destinationId: '16',
                    location: [37.5265, 126.87448],
                  },
                  {
                    destinationId: '17',
                    location: [37.62874, 127.01978],
                  },
                  {
                    destinationId: '18',
                    location: [37.55514, 127.13197],
                  },
                  {
                    destinationId: '19',
                    location: [37.54936, 126.87012],
                  },
                  {
                    destinationId: '20',
                    location: [37.50255, 127.00936],
                  },
                  {
                    destinationId: '21',
                    location: [37.51012, 127.07128],
                  },
                ],
              },
            ],
            newDestination: {
              destinationId: 30,
              location: [37.55805, 126.86601],
            },
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiSortCandidatesWithDistance() {
  return applyDecorators(
    ApiOperation({
      summary: 'Sort candidates with distance',
      description:
        '두지점 (출발지, 도착지) 좌표를 입력 받아 운행하는 차량들의 근접 거리를 기반으로 최적 운송 차량을 결정해 준다.',
    }),

    ApiBody({
      type: SortCandidatesWithDistanceDto,
      examples: {
        locationBasedDispatchExample: {
          summary: 'Sample',
          value: {
            fromLocation: [37.54649, 127.04329],
            toLocation: [37.54659, 127.04229],
            vehicles: [
              {
                vehicleId: '1001',
                vehiclePosition: [37.54649, 127.04329],
                destinations: [
                  {
                    destinationId: '1',
                    location: [37.54649, 127.04329],
                  },
                  {
                    destinationId: '2',
                    location: [37.54578, 127.04246],
                  },
                  {
                    destinationId: '3',
                    location: [37.53837, 127.04608],
                  },
                  {
                    destinationId: '4',
                    location: [37.54655, 127.04162],
                  },
                  {
                    destinationId: '5',
                    location: [37.53837, 127.04608],
                  },
                  {
                    destinationId: '6',
                    location: [37.54655, 127.04162],
                  },
                ],
              },
              {
                vehicleId: '1002',
                vehiclePosition: [37.54649, 127.04329],
                destinations: [
                  {
                    destinationId: '7',
                    location: [37.54578, 126.94347],
                  },
                  {
                    destinationId: '8',
                    location: [37.54578, 127.04694],
                  },
                  {
                    destinationId: '9',
                    location: [37.49931, 127.09096],
                  },
                  {
                    destinationId: '10',
                    location: [37.60143, 127.0128],
                  },
                  {
                    destinationId: '11',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '12',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '13',
                    location: [37.48415, 126.94347],
                  },
                  {
                    destinationId: '14',
                    location: [37.57447, 126.90174],
                  },
                  {
                    destinationId: '15',
                    location: [37.57447, 126.90274],
                  },
                  {
                    destinationId: '16',
                    location: [37.5265, 126.87448],
                  },
                  {
                    destinationId: '17',
                    location: [37.62874, 127.01978],
                  },
                  {
                    destinationId: '18',
                    location: [37.55514, 127.13197],
                  },
                  {
                    destinationId: '19',
                    location: [37.54936, 126.87012],
                  },
                  {
                    destinationId: '20',
                    location: [37.50255, 127.00936],
                  },
                  {
                    destinationId: '21',
                    location: [37.51012, 127.07128],
                  },
                ],
              },
            ],
            newDestination: {
              destinationId: 30,
              location: [37.55805, 126.86601],
            },
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiSortTravelOrder() {
  return applyDecorators(
    ApiOperation({
      summary: 'Sort travel order',
      description: '다수의 방문지 목록을 입력 받아 최적의 방문 순서로 재정열 한다.',
    }),
    ApiBody({
      type: SortTravelOrderDto,
      examples: {
        locationBasedDispatchExample: {
          summary: 'Sample',
          value: {
            vehiclePosition: [37.54649, 127.04329],
            destinations: [
              {
                destinationId: 1,
                location: [37.54649, 127.04329],
              },
              {
                destinationId: 2,
                location: [37.54578, 127.04246],
              },
              {
                destinationId: 3,
                location: [37.53837, 127.04608],
              },
              {
                destinationId: 4,
                location: [37.54655, 127.04162],
              },
              {
                destinationId: 5,
                location: [37.53837, 127.04608],
              },
            ],
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiSortVisitOrder() {
  return applyDecorators(
    ApiOperation({
      summary: 'Sort visit order',
      description: 'WGS84 경위도 좌표들의 방문 순서를 정렬하고 최적 방문 순서에 따른 경로 결과를 반환한다.',
    }),
    ApiBody({
      type: SortVisitOrderDto,
      examples: {
        ApiSortVisitOrderExample: {
          summary: 'Sample',
          value: {
            vehicleType: 'truck',
            routeType: 'recommended',
            avoidOptions: ['uturn', 'ferry'],
            weight: 2000,
            height: 250,
            start: [37.5164, 127.0495],
            destinations: [
              {
                destinationId: '1',
                location: [37.54649, 127.04329],
              },
              {
                destinationId: '2',
                location: [37.54578, 127.04246],
              },
              {
                destinationId: '3',
                location: [37.52837, 127.14608],
              },
              {
                destinationId: '4',
                location: [37.54655, 127.04162],
              },
              {
                destinationId: '5',
                location: [37.53837, 127.04608],
              },
            ],
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

// CargoPlanning API Swagger 데코레이터들

export function ApiCargoPlanning() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Basic',
      description: '기본적으로 kmeans로 방문지 군집화를 통한 화물 계획',
    }),
    ApiBody({
      type: CargoPlanningRequestDto,
      examples: {
        cargoPlanningExample: {
          summary: 'Sample',
          value: {
            vehicles: [
              {
                vid: 1,
                maxcount: 100,
                dimensions: { length: 3000, width: 1800, height: 1500, unit: 'mm' },
                maxweight: { value: 1000000, unit: 'g' },
                location: { dx: 127.04, dy: 37.55 },
              },
            ],
            dests: [
              {
                nid: 101,
                weight: { value: 10, unit: 'kg' },
                dimensions: { length: 10, width: 10, height: 10, unit: 'cm' },
                count: 1,
                location: { dx: 127.041, dy: 37.551 },
              },
              {
                nid: 102,
                weight: { value: 20, unit: 'kg' },
                dimensions: { length: 20, width: 20, height: 20, unit: 'cm' },
                count: 2,
                location: { dx: 127.042, dy: 37.552 },
              },
            ],
            options: {
              vehicleCount: 1,
              capacity: 100,
            },
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Successful response',
      type: CargoPlanningResponseDto,
    }),
  );
}

export function ApiCargoPlanningLimitedWeight() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Limited Weight',
      description:
        '단일 트럭에 복수의 화물을 최대 무게 용량까지 할당. kmeans로 방문지 군집화 후 최대 용량까지 채워 넣음',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningBalancedWeight() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Balanced Weight',
      description: '복수의 트럭에 복수의 화물을 무게에 따라 균등하게 분배. kmeans로 방문지 군집화 후 균등하게 분배',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningLimitedVolume() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Limited Volume',
      description: '단일 트럭에 복수의 화물을 부피 용량까지 할당. kmeans로 방문지 군집화 후 최대 용량까지 채워 넣음',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningBalancedVolume() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Balanced Volume',
      description: '복수의 트럭에 복수의 화물을 부피에 따라 균등하게 분배. kmeans로 방문지 군집화 후 균등하게 분배',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningLimitedCount() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Limited Count',
      description: '단일 트럭에 복수의 화물을 개수까지 할당. kmeans로 방문지 군집화 후 최대 개수까지 채워 넣음',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningBalancedCount() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Balanced Count',
      description: '복수의 트럭에 복수의 화물을 개수에 따라 균등하게 분배. kmeans로 방문지 군집화 후 균등하게 분배',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningLimitedCombine() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Limited Combine',
      description:
        '단일 트럭에 복수의 화물을 무게, 부피, 개수 용량까지 할당. constraints 옵션의 순서에 따라 우선순위가 바뀜 (기본값: weight,volume)',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningBalancedCombine() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Balanced Combine',
      description:
        '복수의 트럭에 복수의 화물을 무게, 부피, 개수에 따라 균등하게 분배. constraints 옵션의 순서에 따라 우선순위가 바뀜 (기본값: weight,volume)',
    }),
    ApiBody({ type: CargoPlanningRequestDto }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}

export function ApiCargoPlanningVisitOrder() {
  return applyDecorators(
    ApiOperation({
      summary: 'Cargo Planning - Visit Order',
      description:
        '군집화되어 각 트럭에 할당된 화물의 방문 순서를 결정. mode 옵션: nearest, 2opt, 3opt (기본값: 2opt). ⚠️ 주의: 이 API는 2단계 프로세스로 사용해야 합니다. 먼저 다른 cargoplanning API로 결과를 생성한 후, 그 결과를 이 API의 입력으로 사용하세요.',
    }),
    ApiBody({
      type: VisitOrderRequestDto,
      examples: {
        visitOrderExample: {
          summary: 'Visit Order Input (클러스터링된 결과 JSON)',
          description: '⚠️ 중요: 원시 차량/목적지 데이터가 아닌, 이미 클러스터링된 결과 JSON을 입력으로 사용',
          value: {
            result: [
              {
                location: [37.55, 127.04],
                nids: [
                  { location: [37.551, 127.041], nid: 6001 },
                  { location: [37.552, 127.042], nid: 6002 },
                  { location: [37.553, 127.043], nid: 6003 },
                ],
                nvid: 1,
              },
              {
                location: [37.55, 127.04],
                nids: [
                  { location: [37.554, 127.044], nid: 6004 },
                  { location: [37.555, 127.045], nid: 6005 },
                ],
                nvid: 2,
              },
            ],
          },
        },
      },
    }),
    ApiResponse({ status: 200, description: 'Successful response', type: CargoPlanningResponseDto }),
  );
}
