// src/controllers/dispatch.controller.ts

import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import {
  LocationBasedDispatchDto,
  SortCandidateWithEtaDto,
  SortCandidatesWithDistanceDto,
  SortCandidatesWithRoutesDto,
  SortTravelOrderDto,
  SortVisitOrderDto,
  TmsMathRequestDto,
  CargoPlanningRequestDto,
  VisitOrderRequestDto,
} from '../types/dispatch.dto';
import { DispatchService } from './dispatch.service';
import { KMeansDispatchService } from './kmeans-dispatch.service';
import {
  ApiLocationBasedDispatch,
  ApiSortCandidateWithEta,
  ApiSortCandidatesWithDistance,
  ApiSortCandidatesWithRoutes,
  ApiSortTravelOrder,
  ApiSortVisitOrder,
  ApiCargoPlanning,
  ApiCargoPlanningLimitedWeight,
  ApiCargoPlanningBalancedWeight,
  ApiCargoPlanningLimitedVolume,
  ApiCargoPlanningBalancedVolume,
  ApiCargoPlanningLimitedCount,
  ApiCargoPlanningBalancedCount,
  ApiCargoPlanningLimitedCombine,
  ApiCargoPlanningBalancedCombine,
  ApiCargoPlanningVisitOrder,
} from './dispatch.swagger';

@ApiTags('Dispatch APIs')
@Controller('')
export class DispatchController {
  constructor(
    private readonly dispatchService: DispatchService,
    private readonly kMeansDispatchService: KMeansDispatchService,
  ) {}

  @Get('dispatch/version')
  getVersion() {
    return this.dispatchService.getVersion();
  }

  @Post('locationbaseddispatch')
  @ApiLocationBasedDispatch()
  locationBasedDispatch(@Body() body: LocationBasedDispatchDto) {
    return this.dispatchService.locationBasedDispatch(body);
  }

  @Post('sortcandidatewitheta')
  @ApiSortCandidateWithEta()
  sortCandidateWithEta(@Body() body: SortCandidateWithEtaDto) {
    return this.dispatchService.sortCandidateWithEta(body);
  }

  @Post('sortcandidateswithroutes')
  @ApiSortCandidatesWithRoutes()
  sortCandidatesWithRoutes(@Body() body: SortCandidatesWithRoutesDto) {
    return this.dispatchService.sortCandidatesWithRoutes(body);
  }

  @Post('sortcandidateswithdistance')
  @ApiSortCandidatesWithDistance()
  sortCandidatesWithDistance(@Body() body: SortCandidatesWithDistanceDto) {
    return this.dispatchService.sortCandidatesWithDistance(body);
  }

  @Post('sorttravelorder')
  @ApiSortTravelOrder()
  sortTravelOrder(@Body() body: SortTravelOrderDto) {
    return this.dispatchService.sortTravelOrder(body);
  }

  @Post('sortvisitorder')
  @ApiSortVisitOrder()
  sortVisitOrder(@Body() body: SortVisitOrderDto) {
    return this.dispatchService.sortVisitOrder(body);
  }

  @ApiExcludeEndpoint()
  @Post('tmsmath')
  tmsMath(@Body() body: TmsMathRequestDto) {
    if (process.env.USE_KMEANS_DISPATCH === 'true') {
      return this.kMeansDispatchService.optimizedBalancedKmeansDispatch(body);
    } else if (process.env.USE_GREEDY_DISPATCH === 'true') {
      return this.kMeansDispatchService.greedyBalancedDispatch(body);
    }
    {
      return this.dispatchService.tmsMath(body);
    }
  }

  // CargoPlanning API 엔드포인트들

  @Post('cargoplanning')
  @ApiCargoPlanning()
  cargoPlanning(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanning(body);
  }

  @Post('cargoplanning/limitedweight')
  @ApiCargoPlanningLimitedWeight()
  cargoPlanningLimitedWeight(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanningLimitedWeight(body);
  }

  @Post('cargoplanning/balancedweight')
  @ApiCargoPlanningBalancedWeight()
  cargoPlanningBalancedWeight(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanningBalancedWeight(body);
  }

  @Post('cargoplanning/limitedvolume')
  @ApiCargoPlanningLimitedVolume()
  cargoPlanningLimitedVolume(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanningLimitedVolume(body);
  }

  @Post('cargoplanning/balancedvolume')
  @ApiCargoPlanningBalancedVolume()
  cargoPlanningBalancedVolume(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanningBalancedVolume(body);
  }

  @Post('cargoplanning/limitedcount')
  @ApiCargoPlanningLimitedCount()
  cargoPlanningLimitedCount(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanningLimitedCount(body);
  }

  @Post('cargoplanning/balancedcount')
  @ApiCargoPlanningBalancedCount()
  cargoPlanningBalancedCount(@Body() body: CargoPlanningRequestDto): Promise<any> {
    return this.dispatchService.cargoPlanningBalancedCount(body);
  }

  @Post('cargoplanning/limitedcombine')
  @ApiCargoPlanningLimitedCombine()
  cargoPlanningLimitedCombine(
    @Body() body: CargoPlanningRequestDto,
    @Query('constraints') constraints?: string,
  ): Promise<any> {
    return this.dispatchService.cargoPlanningLimitedCombine(body, constraints);
  }

  @Post('cargoplanning/balancedcombine')
  @ApiCargoPlanningBalancedCombine()
  cargoPlanningBalancedCombine(
    @Body() body: CargoPlanningRequestDto,
    @Query('constraints') constraints?: string,
  ): Promise<any> {
    return this.dispatchService.cargoPlanningBalancedCombine(body, constraints);
  }

  @Post('cargoplanning/visitorder')
  @ApiCargoPlanningVisitOrder()
  cargoPlanningVisitOrder(@Body() body: VisitOrderRequestDto, @Query('mode') mode?: string): Promise<any> {
    return this.dispatchService.cargoPlanningVisitOrder(body, mode);
  }
}
