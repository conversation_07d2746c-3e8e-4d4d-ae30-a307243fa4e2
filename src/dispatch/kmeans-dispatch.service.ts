import { Injectable, Logger } from '@nestjs/common';
import { kmeans } from 'ml-kmeans';
import { TmsMathRequestDto, TmsMathResponseDto } from '../types/dispatch.dto';

interface Coordinate {
  dx: number;
  dy: number;
  nid?: number;
}

@Injectable()
export class KMeansDispatchService {
  private readonly logger = new Logger(KMeansDispatchService.name);

  private euclideanDistance(a: number[], b: number[]): number {
    return Math.sqrt(a.reduce((sum, _, i) => sum + Math.pow(a[i] - b[i], 2), 0));
  }

  private euclideanDistanceCoord(a: Coordinate, b: Coordinate): number {
    return Math.sqrt(Math.pow(a.dx - b.dx, 2) + Math.pow(a.dy - b.dy, 2));
  }

  public optimizedBalancedKmeansDispatch(request: TmsMathRequestDto): TmsMathResponseDto {
    const { vehicnt, destpos } = request;

    this.logger.log(`balancedKmeansDispatchAlgorithm => vehicnt: ${vehicnt}, cnt: ${destpos.length} `);

    if (destpos.length === 0) {
      throw new Error('No destinations provided');
    }

    const K = Math.min(vehicnt, destpos.length);
    const points = destpos.map((coord) => [coord.dx, coord.dy]);

    let bestClusters: number[][] = [];
    let bestCost = Infinity;
    const maxIterations = 100;
    const targetSize = Math.floor(destpos.length / K);
    const remainder = destpos.length % K;

    for (let iteration = 0; iteration < maxIterations; iteration++) {
      let kmeansResult;

      if (K === 1) {
        kmeansResult = {
          clusters: new Array(points.length).fill(0),
          centroids: [points[0]],
          iterations: 1,
          converged: true,
        };
      } else {
        kmeansResult = kmeans(points, K, {
          maxIterations: 100,
          tolerance: 1e-6,
          distanceFunction: this.euclideanDistance,
        });
      }

      const clusters: number[][] = Array.from({ length: K }, () => []);
      kmeansResult.clusters.forEach((clusterIndex, i) => {
        clusters[clusterIndex].push(i);
      });

      // Optimize clusters
      for (let i = 0; i < 300; i++) {
        // Increase inner loop iterations for thorough optimization
        let improved = false;
        for (let fromCluster = 0; fromCluster < K; fromCluster++) {
          for (let toCluster = 0; toCluster < K; toCluster++) {
            if (fromCluster === toCluster) continue;

            const fromSize = clusters[fromCluster].length;
            const toSize = clusters[toCluster].length;

            if (
              fromSize > targetSize + (fromCluster < remainder ? 1 : 0) &&
              toSize < targetSize + (toCluster < remainder ? 1 : 0)
            ) {
              let bestPointIndex = -1;
              let bestImprovement = -Infinity;

              for (const pointIndex of clusters[fromCluster]) {
                const currentDistance = this.euclideanDistance(points[pointIndex], kmeansResult.centroids[fromCluster]);
                const newDistance = this.euclideanDistance(points[pointIndex], kmeansResult.centroids[toCluster]);
                const improvement = currentDistance - newDistance;

                if (improvement > bestImprovement) {
                  bestImprovement = improvement;
                  bestPointIndex = pointIndex;
                }
              }

              if (bestPointIndex !== -1) {
                clusters[fromCluster] = clusters[fromCluster].filter((p) => p !== bestPointIndex);
                clusters[toCluster].push(bestPointIndex);
                improved = true;
                break;
              }
            }
          }
          if (improved) break;
        }
        if (!improved) break;
      }

      // Calculate cost
      const sizeCost = clusters.reduce((cost, cluster, i) => {
        const targetSizeForCluster = targetSize + (i < remainder ? 1 : 0);
        return cost + Math.abs(cluster.length - targetSizeForCluster);
      }, 0);

      const distanceCost = clusters.reduce((cost, cluster, i) => {
        return (
          cost +
          cluster.reduce((clusterCost, pointIndex) => {
            return clusterCost + this.euclideanDistance(points[pointIndex], kmeansResult.centroids[i]);
          }, 0)
        );
      }, 0);

      const totalCost = sizeCost + distanceCost / destpos.length; // Normalize distance cost

      if (totalCost < bestCost) {
        bestCost = totalCost;
        bestClusters = clusters;
      }
    }

    // Create final result
    const result = bestClusters.map((cluster, index) => ({
      nvid: index + 1,
      nids: cluster.map((pointIndex) => destpos[pointIndex].nid),
    }));

    return {
      command: 'ResClust',
      result: result,
    };
  }

  public greedyBalancedDispatch(request: TmsMathRequestDto): TmsMathResponseDto {
    const { vehicnt, destpos } = request;

    this.logger.log(`greedyBalancedDispatch => vehicnt: ${vehicnt}, cnt: ${destpos.length} `);

    if (destpos.length === 0) {
      throw new Error('No destinations provided');
    }

    const K = Math.min(vehicnt, destpos.length);
    const targetSize = Math.floor(destpos.length / K);
    const remainder = destpos.length % K;

    // Find geographical center
    const center = destpos.reduce((acc, coord) => ({ dx: acc.dx + coord.dx, dy: acc.dy + coord.dy }), { dx: 0, dy: 0 });
    center.dx /= destpos.length;
    center.dy /= destpos.length;

    // Sort destinations by distance from center
    const sortedDest = [...destpos].sort(
      (a, b) => this.euclideanDistanceCoord(a, center) - this.euclideanDistanceCoord(b, center),
    );

    // Initialize clusters with furthest points
    const clusters = sortedDest.slice(-K).map((coord, index) => ({
      nvid: index + 1,
      nids: [coord.nid],
    }));

    // Assign remaining points to nearest cluster
    for (const coord of sortedDest.slice(0, -K)) {
      let nearestCluster = clusters[0];
      let minDistance = this.euclideanDistanceCoord(coord, sortedDest[sortedDest.length - 1]);

      for (const cluster of clusters) {
        const clusterCenter = destpos.find((d) => d.nid === cluster.nids[0])!;
        const distance = this.euclideanDistanceCoord(coord, clusterCenter);
        if (distance < minDistance) {
          minDistance = distance;
          nearestCluster = cluster;
        }
      }

      nearestCluster.nids.push(coord.nid);
    }

    // Balance clusters
    for (let i = 0; i < 1000; i++) {
      // Increase iterations for thorough balancing
      const maxCluster = clusters.reduce((a, b) => (a.nids.length > b.nids.length ? a : b));
      const minCluster = clusters.reduce((a, b) => (a.nids.length < b.nids.length ? a : b));

      if (maxCluster.nids.length <= targetSize + (maxCluster.nvid <= remainder ? 1 : 0)) {
        break; // Balanced
      }

      // Find the point in maxCluster closest to minCluster
      const maxClusterCenter = destpos.find((d) => d.nid === maxCluster.nids[0])!;
      const minClusterCenter = destpos.find((d) => d.nid === minCluster.nids[0])!;

      let pointToMove = maxCluster.nids[1]; // Start from 1 to keep the initial center
      let minDistance = this.euclideanDistanceCoord(destpos.find((d) => d.nid === pointToMove)!, minClusterCenter);

      for (let j = 2; j < maxCluster.nids.length; j++) {
        const distance = this.euclideanDistanceCoord(
          destpos.find((d) => d.nid === maxCluster.nids[j])!,
          minClusterCenter,
        );
        if (distance < minDistance) {
          minDistance = distance;
          pointToMove = maxCluster.nids[j];
        }
      }

      // Move the point
      maxCluster.nids = maxCluster.nids.filter((nid) => nid !== pointToMove);
      minCluster.nids.push(pointToMove);
    }

    return {
      command: 'ResClust',
      result: clusters,
    };
  }
}
