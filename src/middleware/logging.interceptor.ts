import { CallHandler, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { catchError, Observable, tap, throwError } from 'rxjs';
import * as urlencode from 'urlencode';
import { v4 as uuidv4 } from 'uuid';
import { TransactionContext } from '../common/transaction.context';

/**
 * 로깅 인터셉터 클래스
 * 요청과 응답을 로깅하는 기능을 제공합니다.
 */
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);
  static lastVersionResponse: string = '';

  /**
   * 문자열을 지정된 길이로 제한하는 메서드
   * @param obj 변환할 객체
   * @param maxLength 최대 길이
   * @returns 제한된 길이의 문자열
   */
  private stringifyWithEllipsis(obj: any, maxLength: number): string {
    if (!obj) return String(obj);

    try {
      // AxiosResponse 객체인 경우 data 속성만 추출
      if (obj && typeof obj === 'object' && obj.hasOwnProperty('status') && obj.hasOwnProperty('data')) {
        // 로깅 목적으로 status 정보 포함
        const logStatus = `[Status: ${obj.status}]`;

        // data가 빈 문자열이면 그대로 반환
        if (obj.data === '') {
          return `${logStatus} ""`;
        }

        // data 속성만 JSON으로 변환
        const jsonString = JSON.stringify(obj.data);
        return `${logStatus} ${jsonString.length > maxLength ? jsonString.slice(0, 10000) + '...' : jsonString}`;
      }

      // 일반 객체인 경우
      const jsonString = JSON.stringify(obj);
      return jsonString.length > maxLength ? jsonString.slice(0, 10000) + '...' : jsonString;
    } catch (error) {
      // 순환 참조 등으로 인해 JSON.stringify가 실패한 경우
      return `[Object cannot be stringified: ${error.message}]`;
    }
  }

  /**
   * 기본 로그 정보를 생성하는 메서드
   * @param request 요청 객체
   * @param context 실행 컨텍스트
   * @param transactionId 트랜잭션 ID
   * @returns 기본 로그 정보 문자열
   */
  private getBasicLogInfo(request: any, context: ExecutionContext, transactionId: string): string {
    const { method, path: url } = request;
    const query = urlencode.decode(request._parsedUrl?.query || '');
    const body = this.stringifyWithEllipsis(request.body, 100000);
    const ip = request.ip;
    return `[${transactionId}] [${method}] [${url}] [${query}] [${body}] [${ip}] [${context.getClass().name}::${context.getHandler().name}]`;
  }

  /**
   * 응답 로그 정보를 생성하는 메서드
   * @param statusCode 상태 코드
   * @param contentLength 컨텐츠 길이
   * @param duration 처리 시간
   * @param response 응답 객체
   * @returns 응답 로그 정보 문자열
   */
  private getResponseLogInfo(statusCode: number, contentLength: string, duration: number, response: any): string {
    return `[${statusCode}] [${contentLength}] [${duration}ms] <${this.stringifyWithEllipsis(response, 1000)}>`;
  }

  /**
   * 요청과 응답을 로깅하는 메서드
   * @param basicInfo 기본 정보
   * @param responseInfo 응답 정보
   * @param isVersionEndpoint 버전 엔드포인트 여부
   */
  private logRequest(basicInfo: string, responseInfo: string, isVersionEndpoint: boolean = false): void {
    const responseInfoWithoutDuration = responseInfo.replace(/\[\d+ms\]\s/, '');
    if (
      !isVersionEndpoint ||
      (isVersionEndpoint && responseInfoWithoutDuration != LoggingInterceptor.lastVersionResponse)
    ) {
      this.logger.log(`+ ${basicInfo}`);
      this.logger.log(`- ${basicInfo} ${responseInfo}`);
      if (isVersionEndpoint) {
        LoggingInterceptor.lastVersionResponse = responseInfoWithoutDuration;
      }
    }
  }

  /**
   * 인터셉터 메서드
   * @param context 실행 컨텍스트
   * @param next 다음 핸들러
   * @returns Observable 객체
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const transactionId = uuidv4().substring(0, 8);
    const transactionContext = TransactionContext.getInstance();

    return new Observable((subscriber) => {
      transactionContext.run(transactionId, () => {
        const request = context.switchToHttp().getRequest();
        const basicInfo = this.getBasicLogInfo(request, context, transactionId);
        const now = Date.now();

        next
          .handle()
          .pipe(
            tap((res) => {
              const response = context.switchToHttp().getResponse();
              const { statusCode } = response;
              const contentLength = response.get('content-length') || '';
              const duration = Date.now() - now;
              const responseInfo = this.getResponseLogInfo(statusCode, contentLength, duration, res);

              this.logRequest(basicInfo, responseInfo, request.path === '/version');
              subscriber.next(res);
              subscriber.complete();
            }),
            catchError((err: any) => {
              const statusCode = err.status || 500;
              const duration = Date.now() - now;
              const errorInfo = `[${statusCode}] [${duration}ms] [${err.message || err}]`;
              this.logger.error(`- ${basicInfo} ${errorInfo}`);
              subscriber.error(err);
              return throwError(() => err);
            }),
          )
          .subscribe();
      });
    });
  }
}
