import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { AxiosResponse } from 'axios';

/**
 * AxiosResponse 객체를 안전하게 변환하는 인터셉터
 * 순환 참조 문제를 방지하기 위해 필요한 속성만 추출합니다.
 */
@Injectable()
export class ResponseTransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((response) => {
        // AxiosResponse 객체인 경우 HTTP 상태 코드 설정하고 data만 반환
        if (this.isAxiosResponse(response)) {
          const httpResponse = context.switchToHttp().getResponse();
          httpResponse.status(response.status);

          // data만 반환
          return response.data;
        }

        // 그 외의 경우 원본 응답 반환
        return response;
      }),
    );
  }

  /**
   * 객체가 AxiosResponse인지 확인하는 메서드
   * @param obj 확인할 객체
   * @returns AxiosResponse 여부
   */
  private isAxiosResponse(obj: any): obj is AxiosResponse {
    return (
      obj &&
      typeof obj === 'object' &&
      obj.hasOwnProperty('status') &&
      obj.hasOwnProperty('data') &&
      obj.hasOwnProperty('headers') &&
      obj.hasOwnProperty('config')
    );
  }
}
