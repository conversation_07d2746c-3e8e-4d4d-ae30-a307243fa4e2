import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Inject, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    try {
      const request = context.switchToHttp().getRequest();
      const cacheKey = this.generateCacheKey(request);

      const cachedData = await this.cacheManager.get(cacheKey);
      if (cachedData) {
        this.logger.log(`Cache hit for key: ${cacheKey}`);
        return of(cachedData);
      }

      return next.handle().pipe(
        tap(async (data) => {
          await this.setCacheData(cacheKey, data);
        }),
      );
    } catch (error) {
      this.logger.error(`Error in cache interceptor: ${error.message}`, error.stack);
      return next.handle();
    }
  }

  private generateCacheKey(request: any): string {
    const { url, query, body } = request;
    return `${url}:${JSON.stringify(query)}:${JSON.stringify(body)}`;
  }

  private async setCacheData(key: string, data: any): Promise<void> {
    try {
      await this.cacheManager.set(key, data);
    } catch (error) {
      this.logger.error(`Error setting cache: ${error.message}`, error.stack);
    }
  }
}
