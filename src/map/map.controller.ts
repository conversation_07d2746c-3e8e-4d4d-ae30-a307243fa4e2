import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { MapService } from './map.service';
import { ApiGetPng, ApiGetXvg, ApiGetPngAtPoint, ApiGetPngInRange, ApiGetMapVersion } from './map.swagger';

@ApiTags('WebMap APIs')
@Controller('')
export class MapController {
  constructor(private readonly mapService: MapService) {}

  @Get('png')
  @ApiGetPng()
  getPng(
    @Query('zoom') zoom: number,
    @Query('tilex') tilex: number,
    @Query('tiley') tiley: number,
    @Query('region') region: string,
  ) {
    return this.mapService.getPng(zoom, tilex, tiley, region);
  }

  @Get('xvg')
  @ApiGetXvg()
  getXvg(
    @Query('zoom') zoom: number,
    @Query('tilex') tilex: number,
    @Query('tiley') tiley: number,
    @Query('region') region: string,
  ) {
    return this.mapService.getXvg(zoom, tilex, tiley, region);
  }

  @Get('pngatpoint')
  @ApiGetPngAtPoint()
  getPngAtPoint(
    @Query('zoom') zoom: number,
    @Query('lat') lat: number,
    @Query('lng') lng: number,
    @Query('region') region: string,
  ) {
    return this.mapService.getPngAtPoint(zoom, lat, lng, region);
  }

  @Get('pnginrange')
  @ApiGetPngInRange()
  getPngInRange(
    @Query('zoom') zoom: number,
    @Query('lat') lat: number,
    @Query('lng') lng: number,
    @Query('width') width: number,
    @Query('height') height: number,
    @Query('region') region: string,
  ) {
    return this.mapService.getPngInRange(zoom, lat, lng, width, height, region);
  }

  @Get('getmapversion')
  @ApiGetMapVersion()
  getMapVersion(@Query('extn') extn: string, @Query('region') region: string) {
    return this.mapService.getMapVersion(extn, region);
  }
}
