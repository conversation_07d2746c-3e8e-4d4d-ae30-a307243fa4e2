import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { PngAtPointResponseDto, PngInRangeResponseDto } from '../types/map.dto';

export function ApiGetPng() {
  return applyDecorators(
    ApiOperation({
      summary: 'PNG 타일 이미지 조회',
      description:
        '타일 번호를 받아서 웹맵 타일(PNG)을 제공한다. (PNG 이미지 타일은 현재 한국을 대상으로 서비스되고 있음)',
    }),
    ApiQuery({ name: 'zoom', required: true, description: '지도 축척 레벨 (Lv6 ~ Lv18)', example: 12 }),
    ApiQuery({ name: 'tilex', required: true, description: 'X축 타일 번호 (구글 맵 타일 번호와 같음)', example: 3493 }),
    ApiQuery({ name: 'tiley', required: true, description: 'Y축 타일 번호 (구글 맵 타일 번호와 같음)', example: 1586 }),
    ApiQuery({ name: 'region', required: true, description: '지역 (default: kor)', example: 'kor' }),
    ApiResponse({ status: 200, description: 'PNG 이미지 blob', schema: { type: 'string', format: 'binary' } }),
  );
}

export function ApiGetXvg() {
  return applyDecorators(
    ApiOperation({
      summary: 'XVG 타일 데이터 조회',
      description:
        '타일 번호를 받아서 웹맵 타일(압축 SVG)을 제공한다. (압축 SVG 타일은 현재 북미를 대상으로 서비스되고 있음)',
    }),
    ApiQuery({ name: 'zoom', required: true, description: '지도 축척 레벨 (Lv4 ~ Lv18)', example: 12 }),
    ApiQuery({ name: 'tilex', required: true, description: 'X축 타일 번호 (구글 맵 타일 번호와 같음)', example: 1206 }),
    ApiQuery({ name: 'tiley', required: true, description: 'Y축 타일 번호 (구글 맵 타일 번호와 같음)', example: 1540 }),
    ApiQuery({ name: 'region', required: true, description: '지역 (default: kor)', example: 'nam' }),
    ApiResponse({ status: 200, description: '압축된 SVG blob', schema: { type: 'string', format: 'binary' } }),
  );
}

export function ApiGetPngAtPoint() {
  return applyDecorators(
    ApiOperation({
      summary: '특정 좌표의 PNG 이미지 조회',
      description:
        '지도 레벨과 경위도 좌표를 받아서 웹맵 타일(PNG)을 제공한다. (PNG 이미지 타일은 현재 한국을 대상으로 서비스되고 있음)',
    }),
    ApiQuery({ name: 'zoom', required: true, description: '지도 축척 레벨 (Lv6 ~ Lv18)', example: 12 }),
    ApiQuery({ name: 'lat', required: true, description: '위도 (WGS84)', example: 37.542874 }),
    ApiQuery({ name: 'lng', required: true, description: '경도 (WGS84)', example: 127.044929 }),
    ApiQuery({ name: 'region', required: true, description: '지역 (default: kor)', example: 'kor' }),
    ApiResponse({ status: 200, description: 'Successful response', type: PngAtPointResponseDto }),
  );
}

export function ApiGetPngInRange() {
  return applyDecorators(
    ApiOperation({
      summary: '특정 범위의 PNG 이미지 조회',
      description:
        '지도 레벨, 경위도 좌표 그리고 검색 범위를 받아서 웹맵 타일(PNG)을 제공한다. (PNG 이미지 타일은 현재 한국을 대상으로 서비스되고 있음)',
    }),
    ApiQuery({ name: 'zoom', required: true, description: '지도 축척 레벨 (Lv6 ~ Lv18)', example: 12 }),
    ApiQuery({ name: 'lat', required: true, description: '위도 (WGS84)', example: 37.542874 }),
    ApiQuery({ name: 'lng', required: true, description: '경도 (WGS84)', example: 127.044929 }),
    ApiQuery({ name: 'width', required: true, description: '검색 범위 넓이 (~ 2048px)', example: 256 }),
    ApiQuery({ name: 'height', required: true, description: '검색 범위 높이 (~ 2048px)', example: 256 }),
    ApiQuery({ name: 'region', required: true, description: '지역 (default: kor)', example: 'kor' }),
    ApiResponse({ status: 200, description: 'Successful response', type: PngInRangeResponseDto }),
  );
}

export function ApiGetMapVersion() {
  return applyDecorators(
    ApiOperation({ summary: '맵 버전 조회', description: '맵 데이터 버전을 조회한다.' }),
    ApiQuery({ name: 'extn', required: true, description: '타일타입 (png 또는 xvg)', example: 'png' }),
    ApiQuery({ name: 'region', required: true, description: '지역 (kor 또는 nam)', example: 'kor' }),
    ApiResponse({ status: 200, description: '맵 버전 정보', type: String }),
  );
}
