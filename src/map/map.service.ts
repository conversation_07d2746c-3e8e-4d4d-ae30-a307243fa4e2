import { Injectable } from '@nestjs/common';
import { MapApiService } from '../api/map.api.service';
import { PngAtPointResponseDto, PngInRangeResponseDto } from '../types/map.dto';

@Injectable()
export class MapService {
  constructor(private readonly mapApiService: MapApiService) {}

  getPng(zoom: number, tilex: number, tiley: number, region: string) {
    return this.mapApiService.callApi<Buffer>(
      `/png?zoom=${zoom}&tilex=${tilex}&tiley=${tiley}&region=${region}`,
      'GET',
    );
  }

  getXvg(zoom: number, tilex: number, tiley: number, region: string) {
    return this.mapApiService.callApi<string>(
      `/xvg?zoom=${zoom}&tilex=${tilex}&tiley=${tiley}&region=${region}`,
      'GET',
    );
  }

  getPngAtPoint(zoom: number, lat: number, lng: number, region: string) {
    return this.mapApiService.callApi<PngAtPointResponseDto>(
      `/pngatpoint?zoom=${zoom}&lat=${lat}&lng=${lng}&region=${region}`,
      'GET',
    );
  }

  getPngInRange(zoom: number, lat: number, lng: number, width: number, height: number, region: string) {
    return this.mapApiService.callApi<PngInRangeResponseDto>(
      `/pnginrange?zoom=${zoom}&lat=${lat}&lng=${lng}&width=${width}&height=${height}&region=${region}`,
      'GET',
    );
  }

  getMapVersion(extn: string, region: string) {
    return this.mapApiService.callApi<string>(`/getmapversion?extn=${extn}&region=${region}`, 'GET');
  }
}
