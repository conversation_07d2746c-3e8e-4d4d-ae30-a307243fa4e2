// src/decorators/meter-swagger.decorators.ts

import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  CalculateMeterResponseDto,
  CalculateMeterWithAddressesResponseDto,
  CalculateMeterWithViaAddressesResponseDto,
  CalculateMeterWithViaLocationsResponseDto,
} from '../types/meter.dto';

export function ApiCalculateMeterWithLocations() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate distance between two locations',
      description:
        '두 지점(출발지, 도착지) 좌표를 입력 받아 두 지점간의 거리를 옵션에 따라 Euclid Distance, Manhattan Distance, Routing Distance 중 한 가지로 계산하여 결과를 제공한다.',
    }),
    ApiResponse({ status: 200, type: CalculateMeterResponseDto }),
  );
}

export function ApiCalculateMeterWithAddresses() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate distance between two addresses',
      description:
        '두 지점(출발지, 도착지) 주소를 입력 받아 두 지점간의 거리를 옵션에 따라 Euclid Distance, Manhattan Distance, Routing Distance 중 한 가지로 계산하여 결과를 제공한다.',
    }),
    ApiResponse({ status: 200, type: CalculateMeterWithAddressesResponseDto }),
  );
}

export function ApiCalculateMeterWithViaLocations() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate distance with via locations',
      description:
        '두 지점(출발지, 도착지) Geocode 좌표와 중간 경유지들의 방문순서 정렬된 입력 받아 총 예상거리(Routing Distance)를 계산하여 결과를 제공한다.',
    }),
    ApiResponse({
      status: 200,
      type: CalculateMeterWithViaLocationsResponseDto,
    }),
  );
}

export function ApiCalculateMeterWithViaAddresses() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate distance with via addresses',
      description:
        '두 지점 (출발지, 도착지) 주소를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과와 입력된 두 지점의 Geocode 좌표를 제공한다',
    }),
    ApiResponse({
      status: 200,
      type: CalculateMeterWithViaAddressesResponseDto,
    }),
  );
}

export function ApiCalculateTravelTimeWithViaLocations() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate travel time with via locations',
      description:
        '두 지점(출발지, 도착지) 좌표를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과를 제공한다.',
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiCalculateTravelTimeWithAddresses() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate travel time between addresses',
      description:
        '두 지점 (출발지, 도착지) 주소를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과와 입력된 두 지점의 Geocode 좌표를 제공한다',
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiCalculateTravelTimeWithViaAddresses() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate travel time with via addresses',
      description:
        '두 지점 (출발지, 도착지) 주소를 입력 받아 두 지점간의 Routing Distance 거리와 예상 운행시간을 계산하여 결과와 입력된 두 지점의 Geocode 좌표를 제공한다.',
    }),
    ApiResponse({ status: 200, description: 'Successful response' }),
  );
}

export function ApiCalculateMeter() {
  return applyDecorators(
    ApiOperation({
      summary: 'Calculate comprehensive distance and time',
      description:
        '출발지와 도착지 그리고 방문순서가 정련된 주소 목록들을 입력 받아 총 방문에 필요한 예상 거리(경로거리)와 운행시간을 계산해 준다. 각 입력 받은 주소로 Geo-coding을 수행하고 그 계산 결과를 함께 배열로 좌표목록으로 제공한다.',
    }),
    ApiResponse({
      status: 200,
      type: CalculateMeterWithViaAddressesResponseDto,
    }),
  );
}
