// src/controllers/meter.controller.ts

import { Body, Controller, Post, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  CalculateMeterDto,
  CalculateMeterWithAddressesDto,
  CalculateMeterWithLocationsDto,
  CalculateMeterWithViaAddressesDto,
  CalculateMeterWithViaLocationsDto,
  CalculateTravelTimeWithAddressesDto,
  CalculateTravelTimeWithViaLocationsDto,
} from '../types/meter.dto';
import { MeterService } from './meter.service';
import {
  ApiCalculateMeter,
  ApiCalculateMeterWithAddresses,
  ApiCalculateMeterWithLocations,
  ApiCalculateMeterWithViaAddresses,
  ApiCalculateMeterWithViaLocations,
  ApiCalculateTravelTimeWithAddresses,
  ApiCalculateTravelTimeWithViaAddresses,
  ApiCalculateTravelTimeWithViaLocations,
} from './meter.swagger';

@ApiTags('Meter APIs')
@Controller()
export class MeterController {
  constructor(private readonly meterService: MeterService) {}

  @Get('meter/version')
  getVersion() {
    return this.meterService.getVersion();
  }

  @Post('calculateMeterWithLocations')
  @ApiCalculateMeterWithLocations()
  calculateMeterWithLocations(@Body() body: CalculateMeterWithLocationsDto) {
    return this.meterService.calculateMeterWithLocations(body);
  }

  @Post('calculateMeterWithAddresses')
  @ApiCalculateMeterWithAddresses()
  calculateMeterWithAddresses(@Body() body: CalculateMeterWithAddressesDto) {
    return this.meterService.calculateMeterWithAddresses(body);
  }

  @Post('calculateMeterWithViaLocations')
  @ApiCalculateMeterWithViaLocations()
  calculateMeterWithViaLocations(@Body() body: CalculateMeterWithViaLocationsDto) {
    return this.meterService.calculateMeterWithViaLocations(body);
  }

  @Post('calculateMeterWithViaAddresses')
  @ApiCalculateMeterWithViaAddresses()
  calculateMeterWithViaAddresses(@Body() body: CalculateMeterWithViaAddressesDto) {
    return this.meterService.calculateMeterWithViaAddresses(body);
  }

  @Post('calculateTravelTimeWithViaLocations')
  @ApiCalculateTravelTimeWithViaLocations()
  calculateTravelTimeWithViaLocations(@Body() body: CalculateTravelTimeWithViaLocationsDto) {
    return this.meterService.calculateTravelTimeWithViaLocations(body);
  }

  @Post('calculateTravelTimeWithAddresses')
  @ApiCalculateTravelTimeWithAddresses()
  calculateTravelTimeWithAddresses(@Body() body: CalculateTravelTimeWithAddressesDto) {
    return this.meterService.calculateTravelTimeWithAddresses(body);
  }

  @Post('calculateTravelTimeWithViaAddresses')
  @ApiCalculateTravelTimeWithViaAddresses()
  calculateTravelTimeWithViaAddresses(@Body() body: CalculateMeterWithViaAddressesDto) {
    return this.meterService.calculateTravelTimeWithViaAddresses(body);
  }

  @Post('calculateMeter')
  @ApiCalculateMeter()
  calculateMeter(@Body() body: CalculateMeterDto) {
    return this.meterService.calculateMeter(body);
  }
}
