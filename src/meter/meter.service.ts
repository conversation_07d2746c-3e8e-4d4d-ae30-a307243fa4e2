import { Injectable } from '@nestjs/common';
import { MeterApiService } from '../api/meter.api.service';
import {
  CalculateMeterDto,
  CalculateMeterResponseDto,
  CalculateMeterWithAddressesDto,
  CalculateMeterWithAddressesResponseDto,
  CalculateMeterWithLocationsDto,
  CalculateMeterWithViaAddressesDto,
  CalculateMeterWithViaAddressesResponseDto,
  CalculateMeterWithViaLocationsDto,
  CalculateMeterWithViaLocationsResponseDto,
  CalculateTravelTimeWithAddressesDto,
  CalculateTravelTimeWithViaLocationsDto,
} from '../types/meter.dto';

@Injectable()
export class MeterService {
  constructor(private readonly meterApiService: MeterApiService) {}

  async getVersion() {
    return this.meterApiService.callApi<string>('/version', 'GET');
  }

  async calculateMeterWithLocations(body: CalculateMeterWithLocationsDto) {
    return this.meterApiService.callApi<CalculateMeterResponseDto>(
      '/calculateMeterWithLocations',
      'POST',
      undefined,
      body,
    );
  }

  async calculateMeterWithAddresses(body: CalculateMeterWithAddressesDto) {
    return this.meterApiService.callApi<CalculateMeterWithAddressesResponseDto>(
      '/calculateMeterWithAddresses',
      'POST',
      undefined,
      body,
    );
  }

  async calculateMeterWithViaLocations(body: CalculateMeterWithViaLocationsDto) {
    return this.meterApiService.callApi<CalculateMeterWithViaLocationsResponseDto>(
      '/calculateMeterWithViaLocations',
      'POST',
      undefined,
      body,
    );
  }

  async calculateMeterWithViaAddresses(body: CalculateMeterWithViaAddressesDto) {
    return this.meterApiService.callApi<CalculateMeterWithViaAddressesResponseDto>(
      '/calculateMeterWithViaAddresses',
      'POST',
      undefined,
      body,
    );
  }

  async calculateTravelTimeWithViaLocations(body: CalculateTravelTimeWithViaLocationsDto) {
    return this.meterApiService.callApi<CalculateMeterWithViaLocationsResponseDto>(
      '/calculateTravelTimeWithViaLocations',
      'POST',
      undefined,
      body,
    );
  }

  async calculateTravelTimeWithAddresses(body: CalculateTravelTimeWithAddressesDto) {
    return this.meterApiService.callApi<CalculateMeterWithAddressesResponseDto>(
      '/calculateTravelTimeWithAddresses',
      'POST',
      undefined,
      body,
    );
  }

  async calculateTravelTimeWithViaAddresses(body: CalculateMeterWithViaAddressesDto) {
    return this.meterApiService.callApi<CalculateMeterWithViaAddressesResponseDto>(
      '/calculateTravelTimeWithViaAddresses',
      'POST',
      undefined,
      body,
    );
  }

  async calculateMeter(body: CalculateMeterDto) {
    return this.meterApiService.callApi<CalculateMeterWithViaAddressesResponseDto>(
      '/calculateMeter',
      'POST',
      undefined,
      body,
    );
  }
}
