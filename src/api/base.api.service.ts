import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron } from '@nestjs/schedule';
import axios, { AxiosRequestConfig, AxiosResponse, Method } from 'axios';

import { v4 as uuidv4 } from 'uuid';
import { TransactionContext } from '../common/transaction.context';

interface ServerStatus {
  url: string;
  healthy: boolean;
  weight: number;
  callCount: number;
}

@Injectable()
export class BaseApiService implements OnModuleInit {
  private readonly logger = new Logger(BaseApiService.name);

  protected serverStatuses: ServerStatus[] = [];

  constructor(
    protected readonly serviceName: string,
    public configService: ConfigService,
  ) {}

  onModuleInit() {
    const serversString = this.configService.get<string>(`${this.serviceName.toUpperCase()}_SERVERS`);
    const weightsString = this.configService.get<string>(`${this.serviceName.toUpperCase()}_WEIGHTS`);

    if (!serversString) {
      throw new Error(`No servers configured for ${this.serviceName} service`);
    }

    const servers = serversString.split(',');
    let weights: number[];

    if (weightsString) {
      weights = weightsString.split(',').map((w) => {
        const parsed = Number(w);
        if (isNaN(parsed)) {
          this.logger.warn(`Invalid weight value: "${w}" for ${this.serviceName} service. Using default weight 1.`);
          return 1;
        }
        return parsed;
      });
    } else {
      weights = servers.map(() => 1);
    }

    if (servers.length !== weights.length) {
      this.logger.log(
        `Mismatch between number of servers and weights for ${this.serviceName} service. Using default weights.`,
      );
      weights = servers.map(() => 1);
    }

    this.serverStatuses = servers.map((url, index) => ({
      url,
      healthy: false,
      weight: weights[index],
      callCount: 0,
    }));

    this.logger.log(
      `Configured servers for ${this.serviceName} service: ${this.serverStatuses.map((s) => `${s.url} (weight: ${s.weight})`).join(', ')}`,
    );

    this.checkServerHealth();
  }

  @Cron('*/30 * * * * *') // 매 30초마다 실행됨
  async checkServerHealth() {
    for (const status of this.serverStatuses) {
      try {
        await axios.get(`${status.url}/${this.checkEndpoint()}`, { timeout: 3000 });
        if (!status.healthy) {
          this.logger.log(`[ ${this.serviceName} ] Server ${status.url} is now healthy and added back to the list.`);
        }
        status.healthy = true;
      } catch (error) {
        this.logger.error(`[ ${this.serviceName} ] Server ${status.url}/${this.checkEndpoint()} is not responding.`);
        status.healthy = false;
      }
    }
  }

  private getNextHealthyServer(transactionId: string): string {
    try {
      const healthyServers = this.serverStatuses.filter((status) => status.healthy);

      if (healthyServers.length === 0) {
        throw new Error('No healthy servers available');
      }

      const minCallCount = Math.min(...healthyServers.map((server) => server.callCount));
      const serversWithMinCalls = healthyServers.filter((server) => server.callCount === minCallCount);
      const selectedServer = serversWithMinCalls.reduce(
        (prev, current) => (current.weight > prev.weight ? current : prev),
        serversWithMinCalls[0],
      );

      this.logger.log(
        `[${transactionId}] [ ${this.serviceName} ] Selected server: ${selectedServer.url} (calls: ${selectedServer.callCount}, weight: ${selectedServer.weight})`,
      );
      return selectedServer.url;
    } catch (error) {
      this.logger.error(
        `[${transactionId}] Error in getNextHealthyServer: ${error.message}. Using first server as fallback.`,
      );
      return this.serverStatuses[0].url;
    }
  }

  /**
   * API를 호출하고 결과를 반환하는 메서드
   * @param endpoint API 엔드포인트
   * @param method HTTP 메서드 (기본값: GET)
   * @param params 쿼리 파라미터
   * @param data 요청 본문 데이터
   * @param retries 재시도 횟수 (기본값: 3)
   * @returns AxiosResponse 객체 (status, headers, data 등 포함)
   */
  async callApi<T>(
    endpoint: string,
    method: Method = 'GET',
    params?: any,
    data?: any,
    retries = 3,
  ): Promise<AxiosResponse<T>> {
    const transactionContext = TransactionContext.getInstance();
    const transactionId = transactionContext.getTransactionId() || uuidv4().substring(0, 8);
    let lastError;
    let selectedServer: ServerStatus | null = null;

    for (let i = 0; i < retries; i++) {
      try {
        const serverUrl = this.getNextHealthyServer(transactionId);
        selectedServer = this.serverStatuses.find((s) => s.url === serverUrl);

        if (selectedServer) {
          selectedServer.callCount++; // 호출 카운트 증가
        }

        const url = `${serverUrl}${endpoint}`;
        const timeout = this.getTimeout(url, data) + i * 1000;
        this.logger.log(`[${transactionId}][${this.serviceName}] Calling API: ${url} with timeout: ${timeout} ms`);

        const config: AxiosRequestConfig = { method, url, params, data, timeout };
        const response = await axios<T>(config);

        if (selectedServer) {
          selectedServer.callCount--; // 호출 완료 후 카운트 감소
        }

        return response;
      } catch (error) {
        if (selectedServer) {
          selectedServer.callCount--; // 에러 발생 시에도 카운트 감소
          selectedServer.healthy = false;
        }

        this.logger.warn(`[${transactionId}] API call failed on attempt ${i + 1}:`, error);
        lastError = error;
      }
    }

    this.logger.error(`[${transactionId}] API call failed ! ${endpoint}`);
    throw lastError;
  }

  getAvailableServers(): ServerStatus[] {
    return this.serverStatuses.filter((status) => status.healthy);
  }

  getAllServers(): ServerStatus[] {
    return this.serverStatuses;
  }

  protected checkEndpoint(): string {
    return 'version';
  }

  protected getTimeout(url?: string, data?: any): number {
    return this.configService.get<number>('API_TIMEOUT') || 180_000; //3 minutes
  }
}
