import { Global, Module } from '@nestjs/common';

import { DispatchApiService } from '../api/dispatch.api.service';
import { MeterApiService } from '../api/meter.api.service';
import { RouteApiService } from '../api/route.api.service';
import { SearchApiService } from '../api/search.api.service';
import { MapApiService } from './map.api.service';

@Global()
@Module({
  providers: [SearchApiService, DispatchApiService, MeterApiService, RouteApiService, MapApiService],
  exports: [SearchApiService, DispatchApiService, MeterApiService, RouteApiService, MapApiService],
})
export class ApiModule {}
