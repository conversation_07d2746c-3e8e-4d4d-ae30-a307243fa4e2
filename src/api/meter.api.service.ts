import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService } from './base.api.service';

@Injectable()
export class MeterApiService extends BaseApiService {
  constructor(configService: ConfigService) {
    super('meter', configService);
  }

  // getTimeout 메소드 추가
  protected getTimeout(): number {
    return this.configService.get<number>('METER_API_TIMEOUT') || super.getTimeout();
  }
}
