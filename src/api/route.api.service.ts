import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService } from './base.api.service';

@Injectable()
export class RouteApiService extends BaseApiService {
  constructor(configService: ConfigService) {
    super('route', configService);
  }

  // getTimeout 메소드 추가 - 디폴트 2분 타임아웃
  protected getTimeout(): number {
    return this.configService.get<number>('ROUTE_API_TIMEOUT') || 120_000; // 2분 디폴트 타임아웃
  }
}
