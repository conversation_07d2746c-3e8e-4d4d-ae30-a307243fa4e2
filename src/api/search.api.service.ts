import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService } from './base.api.service';
import { GeocodingRequestDto } from '../types/search.dto';

@Injectable()
export class SearchApiService extends BaseApiService {
  private readonly loggerSearch = new Logger(SearchApiService.name);

  constructor(configService: ConfigService) {
    super('search', configService);
  }

  // getTimeout 메소드 수정
  protected getTimeout(url?: string, data?: any): number {
    const maxTimeout = this.configService.get<number>('SEARCH_API_TIMEOUT') || super.getTimeout();

    if (url?.includes('geocoding')) {
      const payload = data as GeocodingRequestDto;
      const baseTimeout = 5_000; // 기본 3초 타임아웃
      const additionalTimeout = (payload?.count || 0) * 100; // count에 따른 추가 타임아웃
      const totalTimeout = Math.min(baseTimeout + additionalTimeout, maxTimeout); // 최대 타임아웃을 넘지 않도록 설정
      this.loggerSearch.log(`[Search getTimeout] url: ${url}, count: ${payload?.count} timeout: ${totalTimeout}ms`);
      return totalTimeout;
    } else if (url?.includes('searchaddress') || url?.includes('extsearch')) {
      const TIMEOUT_PER_REQUEST = 10_000;
      this.loggerSearch.log(`[Search getTimeout] url: ${url}, Timeout:  ${TIMEOUT_PER_REQUEST} ms `);
      return TIMEOUT_PER_REQUEST;
    }

    // 기본 타임아웃 설정
    return maxTimeout;
  }
}
