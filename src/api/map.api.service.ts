import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService } from './base.api.service';

@Injectable()
export class MapApiService extends BaseApiService {
  constructor(configService: ConfigService) {
    super('map', configService);
  }

  checkEndpoint(): string {
    return '/getmapversion';
  }

  // getTimeout 메소드 추가
  protected getTimeout(): number {
    return this.configService.get<number>('MAP_API_TIMEOUT') || super.getTimeout();
  }
}
