import { Module } from '@nestjs/common';
import { RouteController } from './route.controller';
import { RouteService } from './route.service';
import { RouteOptimizerService } from './route-optimizer.service';
import { DispatchModule } from '../dispatch/dispatch.module';

@Module({
  imports: [DispatchModule],
  controllers: [RouteController],
  providers: [RouteService, RouteOptimizerService],
})
export class RouteModule {}
