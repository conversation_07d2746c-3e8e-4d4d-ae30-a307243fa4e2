// src/decorators/route-swagger.decorators.ts

import { applyDecorators } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  CalculateRouteRequestDto,
  TmsRouteResponseDto,
  TmsRouteListRequestDto,
  TmsRouteListResponseDto,
  TmsRouteOnDemandRequestDto,
  TmsRouteRequestDto,
} from '../types/route.dto';

export function ApiTmsRoute() {
  return applyDecorators(
    ApiOperation({ summary: 'route search' }),
    ApiBody({
      type: TmsRouteRequestDto,
      examples: {
        sample: {
          summary: 'Sample TMS Route Request',
          value: {
            command: 'RequestRoute',
            start: {
              dx: 127.16948,
              dy: 37.53785,
            },
            reqid: '1600658759187',
            routeoption: 512,
            destpos: [
              {
                dx: 127.08671,
                dy: 37.53253,
                nid: 1,
              },
            ],
            height: 0,
            weight: 0,
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Successful response',
      type: TmsRouteResponseDto,
    }),
  );
}

export function ApiTmsRouteList() {
  return applyDecorators(
    ApiOperation({ summary: 'calculate multiple routes' }),
    ApiBody({
      type: TmsRouteListRequestDto,
      examples: {
        sample: {
          summary: 'Sample',
          value: {
            command: 'RequestRouteList',
            routeoption: 512,
            routecount: 2,
            routelist: [
              {
                start: {
                  dx: 127.145057,
                  dy: 37.542739,
                },
                destpos: [
                  {
                    nid: '10',
                    dx: 127.046047,
                    dy: 37.547509,
                  },
                ],
              },
              {
                start: {
                  dx: 126.992913,
                  dy: 37.570967,
                },
                destpos: [
                  {
                    nid: '20',
                    dx: 126.89617,
                    dy: 37.52172,
                  },
                ],
              },
            ],
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Successful response',
      type: TmsRouteListResponseDto,
    }),
  );
}

export function ApiTmsRouteOnDemand() {
  return applyDecorators(
    ApiOperation({ summary: 'calculate route on demand' }),
    ApiBody({
      type: TmsRouteOnDemandRequestDto,
      examples: {
        sample: {
          summary: 'Sample',
          value: {
            command: 'RequestRouteOnDemand',
            reqid: '1001',
            start: {
              dx: 127.16948,
              dy: 37.53785,
            },
            new: {
              dx: 127.16948,
              dy: 37.53785,
            },
            routeoption: 512,
            destpos: [
              {
                dx: 127.08671,
                dy: 37.53253,
                nid: 1,
              },
            ],
            height: 0,
            weight: 0,
            islast: false,
            nofirst: false,
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Successful response',
      type: TmsRouteResponseDto,
    }),
  );
}

export function ApiCalculateRoute() {
  return applyDecorators(
    ApiOperation({ summary: 'calculate route' }),
    ApiBody({
      type: CalculateRouteRequestDto,
      examples: {
        sample: {
          summary: 'Sample',
          value: {
            vehicleType: 'truck',
            routeType: 'recommended',
            avoidOptions: ['uturn', 'ferry'],
            weight: 2000,
            height: 250,
            sort: 'enable',
            start: [37.5164, 127.0495],
            destinations: [
              {
                locationId: 1,
                location: [37.50967, 127.02616],
              },
              {
                locationId: 2,
                location: [37.51791, 127.03422],
              },
              {
                locationId: 3,
                location: [37.51929, 127.03989],
              },
            ],
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: 'Successful response',
      type: TmsRouteResponseDto,
    }),
  );
}
