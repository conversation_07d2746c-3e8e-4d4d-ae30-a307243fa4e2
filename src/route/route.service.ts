import { Injectable } from '@nestjs/common';
import { RouteApiService } from '../api/route.api.service';
import {
  CalculateRouteRequestDto,
  CalculateRouteResponseDto,
  GetDriveRangeRequestDto,
  GetDriveRangeResponseDto,
  TmsRouteListRequestDto,
  TmsRouteListResponseDto,
  TmsRouteOnDemandRequestDto,
  TmsRouteRequestDto,
  TmsRouteResponseDto,
} from '../types/route.dto';
import { RouteOptimizerService } from './route-optimizer.service';
import { DispatchService } from '../dispatch/dispatch.service';
import {
  CargoPlanningRequestDto,
  VehicleDto,
  DestinationDto,
  CargoPlanningOptionsDto,
  VisitOrderRequestDto,
  CargoPlanningResultDto,
} from '../types/dispatch.dto';
import { Coordinate, RouteOrder } from '../types/route.dto';

@Injectable()
export class RouteService {
  constructor(
    private readonly routeApiService: RouteApiService,
    private readonly routeOptimizerService: RouteOptimizerService,
    private readonly dispatchService: DispatchService,
  ) {}

  getVersion() {
    return this.routeApiService.callApi<string>('/version', 'GET');
  }

  /**
   * TmsRouteRequestDto를 CargoPlanningRequestDto로 변환
   */
  private convertToCargoPlanning(tmsRequest: TmsRouteRequestDto): CargoPlanningRequestDto {
    // 단일 차량 설정 (출발지 기준)
    const vehicle: VehicleDto = {
      vid: +tmsRequest.reqid,
      location: {
        dx: tmsRequest.start.dx,
        dy: tmsRequest.start.dy,
      },
    };

    // 배송지 목록 변환
    const destinations: DestinationDto[] = tmsRequest.destpos.map((dest, index) => ({
      nid: dest.nid , // nid가 없으면 순차적으로 부여
      location: {
        dx: dest.dx,
        dy: dest.dy,
      },
    }));

    const options: CargoPlanningOptionsDto = {
      vehicleCount: 1,
      capacity: tmsRequest.destpos.length,
    };

    return {
      vehicles: [vehicle],
      dests: destinations,
      options,
    };
  }

  async tmsRouteOptimized(body: TmsRouteRequestDto): Promise<TmsRouteResponseDto> {
    try {
      // 1단계: TmsRouteRequestDto를 CargoPlanningRequestDto로 변환
      const cargoRequest = this.convertToCargoPlanning(body);

      // 2단계: 기본 CargoPlanning API 호출하여 배송지 할당 결과 생성
      const cargoResponse = await this.dispatchService.cargoPlanning(cargoRequest);

      
      let cargoResult = null;
      if ( cargoResponse && cargoResponse.data ) {
        cargoResult = cargoResponse.data;
      }

      if (!cargoResult || !cargoResult.result || cargoResult.result.length === 0) {
        // CargoPlanning 실패 시 기존 tmsRoute로 폴백
        return await this.tmsRoute(body);
      }

      // 3단계: VisitOrder API로 방문 순서 최적화
      const visitOrderRequest: VisitOrderRequestDto = {
        result: cargoResult.result,
      };

      const optimizedResponse = await this.dispatchService.cargoPlanningVisitOrder(visitOrderRequest, '2opt');

      if (!optimizedResponse || !optimizedResponse.result || optimizedResponse.result.length === 0) {
        // VisitOrder 실패 시 기존 tmsRoute로 폴백
        return await this.tmsRoute(body);
      }

      // 4단계: 최적화된 순서대로 순차적 경로 탐색
      const optimizedOrder = optimizedResponse.result[0]; // 단일 차량이므로 첫 번째 결과 사용
      return this.executeSequentialRouting(body, optimizedOrder);
    } catch (error) {
      console.error('tmsRouteOptimized error:', error);
      // 에러 발생 시 기존 tmsRoute로 폴백
      return await this.tmsRoute(body);
    }
  }

  /**
   * 최적화된 순서대로 순차적 경로 탐색 실행
   */
  private async executeSequentialRouting(
    originalRequest: TmsRouteRequestDto,
    optimizedOrder: CargoPlanningResultDto,
  ): Promise<TmsRouteResponseDto> {
    const allOrders: RouteOrder[] = [];
    const allPnts: any[] = [];
    let currentPosition: Coordinate = originalRequest.start;
    let totalEstimate = 0;
    let totalDistance = 0;

    // 최적화된 순서대로 각 배송지에 대해 개별 경로 탐색
    for (let visitIndex = 0; visitIndex < optimizedOrder.nids.length; visitIndex++) {
      const destination = optimizedOrder.nids[visitIndex];

      try {
        // 현재 위치에서 다음 배송지로의 TMS 경로 요청
        const routeRequest: TmsRouteRequestDto = {
          command: originalRequest.command,
          start: currentPosition,
          reqid: `${originalRequest.reqid}-${visitIndex}`,
          routeoption: originalRequest.routeoption,
          destpos: [
            {
              dx: destination.location[1], // 경도
              dy: destination.location[0], // 위도
              nid: destination.nid,
            },
          ],
          height: originalRequest.height,
          weight: originalRequest.weight,
        };

        const segmentResponse = await this.tmsRoute(routeRequest);

        if (segmentResponse && segmentResponse.order && segmentResponse.order.length > 0) {
          const segmentOrder = segmentResponse.order[0];

          // 방문 순서와 누적 시간/거리 업데이트
          const adjustedOrder: RouteOrder = {
            nid: segmentOrder.nid,
            visit: visitIndex + 1,
            estimate: totalEstimate + segmentOrder.estimate,
            dist: totalDistance + segmentOrder.dist,
          };

          allOrders.push(adjustedOrder);

          // 경로 좌표 정보 추가
          if (segmentResponse.pnts) {
            allPnts.push(...segmentResponse.pnts);
          }

          // 누적값 업데이트
          totalEstimate += segmentOrder.estimate;
          totalDistance += segmentOrder.dist;

          // 다음 구간의 시작점을 현재 목적지로 업데이트
          currentPosition = {
            dx: destination.location[1], // 경도
            dy: destination.location[0], // 위도
          };
        }
      } catch (error) {
        console.error(`Error routing to destination ${destination.nid}:`, error);
        // 개별 구간 실패 시 해당 구간은 스킵하고 계속 진행
        continue;
      }
    }

    // 최종 응답 생성
    const response: TmsRouteResponseDto = {
      command: 'ResponseRoute',
      resid: +originalRequest.reqid || 1,
      order: allOrders,
      pnts: allPnts, // 모든 구간의 경로 좌표를 배열로 저장
    };

    return response;
  }

  /**
   * TMS 경로 탐색 (이전 방식)
   * 최적화 로직 없이 직접 API 호출
   */
  async tmsRoute(body: TmsRouteRequestDto): Promise<TmsRouteResponseDto> {
    const response = await this.routeApiService.callApi<TmsRouteResponseDto>('/tmsroute', 'POST', undefined, body, 1);
    return response.data;
  }

  tmsRouteList(body: TmsRouteListRequestDto) {
    return this.routeApiService.callApi<TmsRouteListResponseDto>('/tmsroutelist', 'POST', undefined, body, 1);
  }

  tmsRouteOnDemand(body: TmsRouteOnDemandRequestDto) {
    return this.routeApiService.callApi<TmsRouteResponseDto>('/tmsrouteondemand', 'POST', undefined, body, 1);
  }

  calculateRoute(body: CalculateRouteRequestDto) {
    return this.routeApiService.callApi<CalculateRouteResponseDto>('/calculateRoute', 'POST', undefined, body);
  }

  getDriveRange(body: GetDriveRangeRequestDto) {
    return this.routeApiService.callApi<GetDriveRangeResponseDto>('/getdriverange', 'POST', undefined, body);
  }
}
