// src/controllers/route.controller.ts

import { Body, Controller, Post, Get } from '@nestjs/common';
import { ApiExcludeEndpoint, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  CalculateRouteRequestDto,
  GetDriveRangeRequestDto,
  TmsRouteListRequestDto,
  TmsRouteOnDemandRequestDto,
  TmsRouteRequestDto,
  OptimizedTmsRouteRequestDto,
} from '../types/route.dto';
import { RouteService } from './route.service';
import { ApiCalculateRoute, ApiTmsRoute, ApiTmsRouteList, ApiTmsRouteOnDemand } from './route.swagger';

@ApiTags('Route APIs')
@Controller()
export class RouteController {
  constructor(private readonly routeService: RouteService) {}

  @Get('route/version')
  getVersion() {
    return this.routeService.getVersion();
  }

  @Post('tmsroute')
  @ApiTmsRoute()
  tmsRoute(@Body() body: TmsRouteRequestDto) {
    return this.routeService.tmsRouteOptimized(body); //TODO 이후에 최적화
  }

  @Post('tmsroute-old')
  @ApiTmsRoute()
  tmsRouteOld(@Body() body: TmsRouteRequestDto) {
    return this.routeService.tmsRoute(body);
  }

  @Post('tmsroutelist')
  @ApiTmsRouteList()
  tmsRouteList(@Body() body: TmsRouteListRequestDto) {
    return this.routeService.tmsRouteList(body);
  }

  @Post('tmsrouteondemand')
  @ApiTmsRouteOnDemand()
  tmsRouteOnDemand(@Body() body: TmsRouteOnDemandRequestDto) {
    return this.routeService.tmsRouteOnDemand(body);
  }

  @Post('calculateRoute')
  @ApiCalculateRoute()
  calculateRoute(@Body() body: CalculateRouteRequestDto) {
    return this.routeService.calculateRoute(body);
  }

  @Post('getdriverange')
  @ApiExcludeEndpoint()
  getDriveRange(@Body() body: GetDriveRangeRequestDto) {
    return this.routeService.getDriveRange(body);
  }
}
