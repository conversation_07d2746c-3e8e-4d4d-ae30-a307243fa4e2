import { Injectable } from '@nestjs/common';
import {
  PostalCodeGroup,
  GroupRouteResult,
  RequestCoordinate,
  Coordinate,
  RouteOrder,
  TmsRouteRequestDto,
  TmsRouteResponseDto,
} from '../types/route.dto';
import { RouteApiService } from '../api/route.api.service';

@Injectable()
//deprecated
export class RouteOptimizerService {
  constructor(private readonly routeApiService: RouteApiService) {}

  /**
   * 배송지를 우편번호로 그룹핑
   */
  groupDestinationsByPostalCode(
    destinations: RequestCoordinate[],
    start: Coordinate,
    usePostalCodeGrouping: boolean,
  ): PostalCodeGroup[] {
    const maxGroupSize = 10; // 우편번호 없는 그룹에만 적용

    if (!usePostalCodeGrouping) {
      // 우편번호 그룹핑을 사용하지 않는 경우, 크기 기준으로만 분할
      return this.splitBySize(destinations, start, maxGroupSize);
    }

    const groupMap = new Map<string, RequestCoordinate[]>();

    // 우편번호별로 그룹핑 (앞 4자리만 사용)
    destinations.forEach((dest) => {
      let postalCode = dest.postalCode || 'NO_POSTAL_CODE';

      // 우편번호가 있는 경우 앞 4자리만 추출
      if (postalCode !== 'NO_POSTAL_CODE' && postalCode.length >= 4) {
        postalCode = postalCode.substring(0, 4);
      }

      if (!groupMap.has(postalCode)) {
        groupMap.set(postalCode, []);
      }
      groupMap.get(postalCode)!.push(dest);
    });

    const groups: PostalCodeGroup[] = [];

    // 각 우편번호 그룹 처리
    groupMap.forEach((groupDestinations, postalCode) => {
      if (postalCode === 'NO_POSTAL_CODE') {
        // 우편번호가 없는 그룹: 출발점으로부터 직선거리 순으로 정렬 후 10개씩 분할
        const sortedDestinations = this.sortDestinationsByDistance(groupDestinations, start);
        const subGroups = this.splitSortedDestinations(sortedDestinations, start, maxGroupSize);
        groups.push(...subGroups);

        console.log(
          `NO_POSTAL_CODE 그룹: ${groupDestinations.length}개 배송지를 거리순 정렬 후 ${subGroups.length}개 그룹으로 분할`,
        );
      } else {
        // 우편번호가 있는 그룹은 크기 제한 없이 모든 배송지를 한 번에 처리
        groups.push(this.createPostalCodeGroup(postalCode, groupDestinations, start));
      }
    });

    return groups;
  }

  /**
   * 크기 기준으로만 분할 (우편번호 무시)
   */
  private splitBySize(destinations: RequestCoordinate[], start: Coordinate, maxGroupSize: number): PostalCodeGroup[] {
    const groups: PostalCodeGroup[] = [];

    for (let i = 0; i < destinations.length; i += maxGroupSize) {
      const chunk = destinations.slice(i, i + maxGroupSize);
      groups.push(this.createPostalCodeGroup(`GROUP_${Math.floor(i / maxGroupSize) + 1}`, chunk, start));
    }

    return groups;
  }

  /**
   * 배송지를 한붓그리기 방식으로 정렬 (현재 위치에서 가장 가까운 다음 좌표 선택)
   */
  private sortDestinationsByDistance(destinations: RequestCoordinate[], start: Coordinate): RequestCoordinate[] {
    if (destinations.length === 0) return [];

    const sortedDestinations: RequestCoordinate[] = [];
    const remainingDestinations = [...destinations]; // 남은 배송지들
    let currentPosition: Coordinate = start; // 현재 위치

    console.log(`한붓그리기 방식 정렬 시작 - 총 ${destinations.length}개 배송지`);
    console.log(`출발점: [${start.dx}, ${start.dy}]`);

    while (remainingDestinations.length > 0) {
      // 현재 위치에서 가장 가까운 배송지 찾기
      let nearestIndex = 0;
      let nearestDistance = this.calculateDistance(currentPosition, remainingDestinations[0]);

      for (let i = 1; i < remainingDestinations.length; i++) {
        const distance = this.calculateDistance(currentPosition, remainingDestinations[i]);
        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestIndex = i;
        }
      }

      // 가장 가까운 배송지를 선택하고 제거
      const nearestDestination = remainingDestinations.splice(nearestIndex, 1)[0];
      sortedDestinations.push(nearestDestination);

      console.log(
        `  ${sortedDestinations.length}. nid:${nearestDestination.nid} 거리:${nearestDistance.toFixed(0)}m [${nearestDestination.dx}, ${nearestDestination.dy}]`,
      );

      // 현재 위치를 선택된 배송지로 업데이트
      currentPosition = nearestDestination;
    }

    console.log(`한붓그리기 정렬 완료 - ${sortedDestinations.length}개 배송지 정렬됨`);
    return sortedDestinations;
  }

  /**
   * 정렬된 배송지를 지정된 크기로 순서대로 분할
   */
  private splitSortedDestinations(
    sortedDestinations: RequestCoordinate[],
    start: Coordinate,
    maxGroupSize: number,
  ): PostalCodeGroup[] {
    const groups: PostalCodeGroup[] = [];

    for (let i = 0; i < sortedDestinations.length; i += maxGroupSize) {
      const chunk = sortedDestinations.slice(i, i + maxGroupSize);
      const subGroupName = `NO_POSTAL_CODE_${Math.floor(i / maxGroupSize) + 1}`;
      groups.push(this.createPostalCodeGroup(subGroupName, chunk, start));

      console.log(
        `  그룹 ${Math.floor(i / maxGroupSize) + 1}: ${chunk.length}개 배송지 (nid: ${chunk.map((d) => d.nid).join(', ')})`,
      );
    }

    return groups;
  }

  /**
   * PostalCodeGroup 생성
   */
  private createPostalCodeGroup(
    postalCode: string,
    destinations: RequestCoordinate[],
    start: Coordinate,
  ): PostalCodeGroup {
    // 대표 좌표는 더 이상 사용하지 않으므로 첫 번째 배송지를 임시로 설정
    // 실제로는 findNearestGroup에서 모든 배송지를 개별적으로 체크함
    const representative = destinations[0];
    const distanceFromStart = this.calculateDistance(start, representative);

    return {
      postalCode,
      destinations,
      representative,
      distanceFromStart,
    };
  }

  /**
   * 두 좌표 간 거리 계산 (직선 거리)
   */
  calculateDistance(coord1: Coordinate, coord2: Coordinate): number {
    // 간단한 직선 거리 계산 (피타고라스 정리)
    const deltaX = coord2.dx - coord1.dx;
    const deltaY = coord2.dy - coord1.dy;

    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  /**
   * 그룹을 단계별로 최적화하여 경로 탐색 수행
   * 가장 가까운 그룹을 선택하고 즉시 라우팅하는 방식으로 진행
   */
  async processGroupRoutesStepByStep(
    originalRequest: TmsRouteRequestDto,
    groups: PostalCodeGroup[],
  ): Promise<GroupRouteResult[]> {
    const groupResults: GroupRouteResult[] = [];
    let currentStart = originalRequest.start;
    let currentPostalCode: string | undefined = undefined; // 현재 그룹의 우편번호
    const remainingGroups = [...groups]; // 남은 그룹들

    console.log(`=== 단계별 그룹 처리 시작 (총 ${groups.length}개 그룹) ===`);

    for (let step = 0; step < groups.length; step++) {
      // 현재 위치에서 가장 가까운 그룹 찾기 (우편번호 유사성 고려)
      const nextGroup = this.findNearestGroup(currentStart, remainingGroups, currentPostalCode);

      if (!nextGroup) {
        console.error(`단계 ${step + 1}: 다음 그룹을 찾을 수 없음`);
        break;
      }

      // 선택된 그룹을 남은 그룹에서 제거
      const groupIndex = remainingGroups.indexOf(nextGroup);
      remainingGroups.splice(groupIndex, 1);

      console.log(`단계 ${step + 1}/${groups.length}: ${nextGroup.postalCode} 그룹 선택`);
      console.log(`현재 시작점: [${currentStart.dx}, ${currentStart.dy}]`);
      console.log(`그룹 대표점: [${nextGroup.representative.dx}, ${nextGroup.representative.dy}]`);
      console.log(`그룹 배송지 수: ${nextGroup.destinations.length}개`);

      // 선택된 그룹의 경로 탐색 수행
      const groupRequest: TmsRouteRequestDto = {
        ...originalRequest,
        start: currentStart,
        destpos: nextGroup.destinations,
      };

      try {
        console.log(`API 호출 중... (단계 ${step + 1})`);
        const groupResponse = await this.routeApiService.callApi<TmsRouteResponseDto>(
          '/tmsroute',
          'POST',
          undefined,
          groupRequest,
          1,
        );

        console.log(`API 응답 상태:`, groupResponse?.status);

        // 응답 데이터 검증 및 처리
        if (groupResponse && groupResponse.data) {
          const route = groupResponse.data;

          console.log(`응답 데이터 상세:`, {
            command: route.command,
            resid: route.resid,
            hasOrder: !!route.order,
            orderLength: route.order?.length || 0,
            hasPnts: !!route.pnts,
            pntsType: Array.isArray(route.pnts) ? 'array' : typeof route.pnts,
          });

          // order 검증
          if (!route.order || !Array.isArray(route.order) || route.order.length === 0) {
            console.error(`단계 ${step + 1}: 빈 order 배열 반환됨`);
            console.error(`요청 데이터:`, JSON.stringify(groupRequest, null, 2));
            continue;
          }

          // pnts 검증
          if (!route.pnts) {
            console.warn(`단계 ${step + 1}: pnts가 없음`);
            route.pnts = [[]]; // 기본값 설정
          } else if (Array.isArray(route.pnts)) {
            if (route.pnts.length === 0) {
              console.warn(`단계 ${step + 1}: 빈 pnts 배열`);
              route.pnts = [[]]; // 기본값 설정
            }
          } else {
            console.warn(`단계 ${step + 1}: pnts가 배열이 아님:`, typeof route.pnts);
          }

          console.log(`단계 ${step + 1} 성공: order ${route.order.length}개, pnts 타입: ${typeof route.pnts}`);

          // 그룹 결과 생성
          const groupResult: GroupRouteResult = {
            postalCode: nextGroup.postalCode,
            order: route.order,
            pnts: route.pnts,
            firstDestination: this.getDestinationByOrder(nextGroup.destinations, route.order, 1),
            lastDestination: this.getDestinationByOrder(nextGroup.destinations, route.order, route.order.length),
          };

          groupResults.push(groupResult);

          // 다음 단계의 시작점을 현재 그룹의 마지막 목적지로 설정
          if (groupResult.lastDestination) {
            currentStart = {
              dx: groupResult.lastDestination.dx,
              dy: groupResult.lastDestination.dy,
            };
            console.log(`다음 시작점 설정: [${currentStart.dx}, ${currentStart.dy}]`);
          }

          // 현재 그룹의 우편번호를 다음 단계에서 사용하도록 설정
          currentPostalCode = nextGroup.postalCode;
          console.log(`현재 우편번호 업데이트: ${currentPostalCode}`);

          // 남은 그룹들과의 거리 정보 출력 (디버깅용)
          if (remainingGroups.length > 0) {
            console.log(`남은 그룹들:`);
            remainingGroups.forEach((g) => {
              // 각 그룹의 가장 가까운 배송지 찾기
              let minDistance = Infinity;
              let nearestDest: RequestCoordinate | null = null;

              g.destinations.forEach((dest) => {
                const distance = this.calculateDistance(currentStart, dest);
                if (distance < minDistance) {
                  minDistance = distance;
                  nearestDest = dest;
                }
              });

              console.log(
                `  - ${g.postalCode}: 가장 가까운 배송지 거리 ${minDistance.toFixed(0)}m (nid:${nearestDest?.nid})`,
              );
            });
          }
        } else {
          console.error(`단계 ${step + 1}: 유효하지 않은 API 응답`);
          console.error(`응답 구조:`, {
            hasResponse: !!groupResponse,
            hasData: !!groupResponse?.data,
            responseType: typeof groupResponse,
            dataType: typeof groupResponse?.data,
          });
        }
      } catch (error) {
        console.error(`단계 ${step + 1} API 호출 오류:`, error);
        // 오류가 발생해도 다음 그룹 처리 계속
        continue;
      }
    }

    console.log(`=== 단계별 그룹 처리 완료 (성공: ${groupResults.length}/${groups.length}) ===`);
    return groupResults;
  }

  /**
   * 현재 위치에서 가장 가까운 그룹 찾기 (속도 최적화)
   * 1. 우편번호 차이가 가장 적은 5개 그룹을 먼저 선별
   * 2. 선별된 그룹에서 가장 가까운 배송지가 있는 그룹 1개 선택
   */
  private findNearestGroup(
    currentPosition: Coordinate,
    groups: PostalCodeGroup[],
    currentPostalCode?: string,
  ): PostalCodeGroup | null {
    if (groups.length === 0) {
      return null;
    }

    let targetGroups = groups;

    // 현재 우편번호가 있는 경우 우편번호 차이가 가장 적은 5개 그룹 선별
    if (currentPostalCode) {
      const currentPostalNum = this.extractPostalNumber(currentPostalCode);
      console.log(`현재 우편번호: ${currentPostalCode} -> 숫자: ${currentPostalNum}`);

      if (currentPostalNum !== null) {
        // 각 그룹의 우편번호 차이 계산
        const groupsWithPostalDiff = groups.map((group) => {
          const groupPostalNum = this.extractPostalNumber(group.postalCode);
          const postalDiff = groupPostalNum !== null ? Math.abs(currentPostalNum - groupPostalNum) : Infinity;

          console.log(
            `그룹 우편번호 비교: ${group.postalCode} (${groupPostalNum}) vs 현재 (${currentPostalNum}) = 차이: ${postalDiff}`,
          );

          return { group, postalDiff, groupPostalNum };
        });

        // 우편번호 차이 순으로 정렬하여 상위 5개 선별
        groupsWithPostalDiff.sort((a, b) => a.postalDiff - b.postalDiff);
        const top5Groups = groupsWithPostalDiff.slice(0, Math.min(5, groupsWithPostalDiff.length));

        targetGroups = top5Groups.map((item) => item.group);

        console.log(`우편번호 차이 기준 상위 ${targetGroups.length}개 그룹 선별:`);
        top5Groups.forEach((item, index) => {
          const diffText = item.postalDiff === Infinity ? '무효' : item.postalDiff.toString();
          const groupNumText = item.groupPostalNum !== null ? item.groupPostalNum.toString() : '무효';
          console.log(`  ${index + 1}. ${item.group.postalCode} (${groupNumText}) - 차이: ${diffText}`);
        });
      } else {
        console.log(`현재 우편번호 추출 실패 - 전체 그룹 대상으로 검색`);
      }
    } else {
      // 최초 시작 시 (현재 우편번호 없음) - 전체 그룹에서 가장 가까운 배송지 검색
      console.log(`최초 시작점 - 전체 ${groups.length}개 그룹에서 가장 가까운 배송지 검색:`);
      console.log(`  현재 위치: [${currentPosition.dx}, ${currentPosition.dy}]`);
      console.log(`  대상 그룹들: ${groups.map((g) => g.postalCode).join(', ')}`);
    }

    // 선별된 그룹에서 가장 가까운 배송지가 있는 그룹 찾기
    let nearestGroup: PostalCodeGroup | null = null;
    let nearestDestination: RequestCoordinate | null = null;
    let minDistance = Infinity;

    // 최초 시작 시 각 그룹별 가장 가까운 배송지 정보 출력
    if (!currentPostalCode) {
      console.log(`각 그룹별 가장 가까운 배송지 검색 결과:`);
    }

    for (const group of targetGroups) {
      let groupMinDistance = Infinity;
      let groupNearestDest: RequestCoordinate | null = null;

      for (const destination of group.destinations) {
        const distance = this.calculateDistance(currentPosition, destination);
        if (distance < groupMinDistance) {
          groupMinDistance = distance;
          groupNearestDest = destination;
        }
        if (distance < minDistance) {
          minDistance = distance;
          nearestGroup = group;
          nearestDestination = destination;
        }
      }

      // 최초 시작 시 각 그룹의 결과 출력
      if (!currentPostalCode && groupNearestDest) {
        console.log(
          `  - ${group.postalCode}: 가장 가까운 배송지 거리 ${groupMinDistance.toFixed(0)}m (nid:${groupNearestDest.nid})`,
        );
      }
    }

    if (nearestGroup && nearestDestination) {
      const selectionType = targetGroups.length < groups.length ? '우편번호 필터링' : '전체 그룹';
      console.log(`가장 가까운 그룹 선택 (${selectionType}): ${nearestGroup.postalCode}`);
      console.log(
        `  - 선택된 배송지: nid=${nearestDestination.nid} [${nearestDestination.dx}, ${nearestDestination.dy}]`,
      );
      console.log(`  - 거리: ${minDistance.toFixed(0)}m`);
      console.log(`  - 그룹 내 총 배송지 수: ${nearestGroup.destinations.length}개`);

      if (currentPostalCode) {
        const currentPostalNum = this.extractPostalNumber(currentPostalCode);
        const selectedPostalNum = this.extractPostalNumber(nearestGroup.postalCode);
        if (currentPostalNum !== null && selectedPostalNum !== null) {
          const postalDiff = Math.abs(currentPostalNum - selectedPostalNum);
          console.log(`  - 우편번호 차이: ${currentPostalNum} vs ${selectedPostalNum} = ${postalDiff}`);
        } else {
          console.log(`  - 우편번호 차이 계산 실패: 현재(${currentPostalNum}) 선택(${selectedPostalNum})`);
        }
      }
    }

    return nearestGroup;
  }

  /**
   * 우편번호에서 숫자 부분 추출 (5자리 우편번호)
   */
  private extractPostalNumber(postalCode: string): number | null {
    if (!postalCode || postalCode === 'NO_POSTAL_CODE') {
      console.log(`우편번호 추출 실패: 유효하지 않은 우편번호 (${postalCode})`);
      return null;
    }

    // 우편번호에서 숫자만 추출
    const numbers = postalCode.replace(/\D/g, '');

    if (numbers.length === 0) {
      console.log(`우편번호 추출 실패: 숫자가 없는 우편번호 (${postalCode})`);
      return null;
    }

    // 5자리 우편번호 기준으로 처리
    if (numbers.length < 5) {
      console.log(`우편번호 추출 경고: 5자리 미만의 우편번호 (${postalCode} -> ${numbers})`);
      // 5자리가 안 되는 경우 앞에 0을 채워서 5자리로 만듦
      const paddedNumbers = numbers.padStart(5, '0');
      const postalNum = parseInt(paddedNumbers);
      console.log(`우편번호 패딩 처리: ${numbers} -> ${paddedNumbers} -> ${postalNum}`);
      return isNaN(postalNum) ? null : postalNum;
    }

    // 5자리 이상인 경우 앞 5자리만 사용
    const postalNum = parseInt(numbers.substring(0, 5));

    if (isNaN(postalNum)) {
      console.log(`우편번호 추출 실패: 숫자 변환 오류 (${postalCode} -> ${numbers})`);
      return null;
    }

    console.log(`우편번호 추출 성공: ${postalCode} -> ${numbers} -> ${postalNum}`);
    return postalNum;
  }

  /**
   * @deprecated 기존 sortGroupsByDistance 메서드 - processGroupRoutesStepByStep 사용 권장
   */
  async sortGroupsByDistance(groups: PostalCodeGroup[], start: Coordinate): Promise<PostalCodeGroup[]> {
    console.log(`=== 기존 그룹 정렬 방식 (deprecated) - 거리 기반 정렬 ===`);
    const distanceSorted = groups.sort((a, b) => a.distanceFromStart - b.distanceFromStart);
    console.log(
      '거리 기반 정렬 결과:',
      distanceSorted.map((g, index) => `${index + 1}. ${g.postalCode} (거리: ${g.distanceFromStart.toFixed(0)}m)`),
    );
    return distanceSorted;
  }

  /**
   * @deprecated 기존 processGroupRoutes 메서드 - processGroupRoutesStepByStep 사용 권장
   */
  async processGroupRoutes(
    originalRequest: TmsRouteRequestDto,
    sortedGroups: PostalCodeGroup[],
  ): Promise<GroupRouteResult[]> {
    console.log(`=== 기존 그룹 처리 방식 (deprecated) - processGroupRoutesStepByStep 사용 권장 ===`);
    return this.processGroupRoutesStepByStep(originalRequest, sortedGroups);
  }

  /**
   * 순서에 따른 목적지 찾기
   */
  private getDestinationByOrder(
    destinations: RequestCoordinate[],
    orders: RouteOrder[],
    visitOrder: number,
  ): RequestCoordinate | null {
    const orderItem = orders.find((order) => order.visit === visitOrder);
    if (!orderItem) return null;

    return destinations.find((dest) => dest.nid?.toString() === orderItem.nid) || null;
  }

  /**
   * 그룹 결과를 최종 결과로 취합
   */
  mergeGroupResults(groupResults: GroupRouteResult[]): { order: RouteOrder[]; pnts: any } {
    const mergedOrder: RouteOrder[] = [];
    let mergedPnts: any = [];
    let currentVisitOrder = 1;

    // 각 그룹의 결과를 순서대로 병합
    groupResults.forEach((groupResult) => {
      // 순서 정보 병합 (visit 번호 재조정)
      groupResult.order.forEach((order) => {
        mergedOrder.push({
          ...order,
          visit: currentVisitOrder++,
        });
      });

      // 좌표 정보 병합 - 실제 API 응답 형식에 따라 유연하게 처리
      if (groupResult.pnts) {
        if (Array.isArray(groupResult.pnts)) {
          if (groupResult.pnts.length > 0) {
            if (Array.isArray(groupResult.pnts[0])) {
              // pnts가 [Coordinate[]] 형식인 경우
              if (Array.isArray(mergedPnts)) {
                mergedPnts.push(...groupResult.pnts[0]);
              } else {
                mergedPnts = [groupResult.pnts[0]];
              }
            } else {
              // pnts가 Coordinate[] 형식인 경우
              if (Array.isArray(mergedPnts)) {
                mergedPnts.push(...groupResult.pnts);
              } else {
                mergedPnts = groupResult.pnts;
              }
            }
          }
        } else {
          // pnts가 다른 형식인 경우 그대로 추가
          if (Array.isArray(mergedPnts)) {
            mergedPnts.push(groupResult.pnts);
          } else {
            mergedPnts = [groupResult.pnts];
          }
        }
      }
    });

    // 최종 형식 결정
    if (Array.isArray(mergedPnts) && mergedPnts.length > 0 && !Array.isArray(mergedPnts[0])) {
      // 단일 배열인 경우 이중 배열로 감싸기
      mergedPnts = [mergedPnts];
    } else if (!Array.isArray(mergedPnts)) {
      // 배열이 아닌 경우 빈 배열로 초기화
      mergedPnts = [[]];
    }

    return { order: mergedOrder, pnts: mergedPnts };
  }

  /**
   * 요청이 최적화가 필요한지 판단
   */
  shouldOptimize(destinations: RequestCoordinate[], threshold: number = 10): boolean {
    return destinations.length > threshold;
  }
}
