import { AsyncLocalStorage } from 'async_hooks';

export class TransactionContext {
  private static instance: TransactionContext;
  private storage: AsyncLocalStorage<Map<string, any>>;

  private constructor() {
    this.storage = new AsyncLocalStorage<Map<string, any>>();
  }

  static getInstance(): TransactionContext {
    if (!TransactionContext.instance) {
      TransactionContext.instance = new TransactionContext();
    }
    return TransactionContext.instance;
  }

  getTransactionId(): string {
    const store = this.storage.getStore();
    return store?.get('transactionId');
  }

  setTransactionId(transactionId: string): void {
    const store = this.storage.getStore();
    if (store) {
      store.set('transactionId', transactionId);
    }
  }

  run(transactionId: string, callback: () => any): any {
    const store = new Map<string, any>();
    store.set('transactionId', transactionId);
    return this.storage.run(store, callback);
  }
}
