import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { TransactionContext } from './transaction.context';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const transactionContext = TransactionContext.getInstance();
    const transactionId = transactionContext.getTransactionId() || 'unknown';

    let status: number;
    let message: string;
    let errorDetails: any;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const errorResponse = exception.getResponse();
      message = typeof errorResponse === 'string' ? errorResponse : (errorResponse as any).message || 'HTTP Exception';
      errorDetails = typeof errorResponse === 'object' ? errorResponse : {};
    } else if (exception instanceof Error) {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = exception.message || 'Internal Server Error';
      errorDetails = {
        name: exception.name,
        stack: process.env.NODE_ENV === 'development' ? exception.stack : undefined,
      };
    } else {
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      message = 'Unknown Error';
      errorDetails = { exception: String(exception) };
    }

    // 에러 로깅
    this.logger.error(`[${transactionId}] ${request.method} ${request.url} - Status: ${status} - Message: ${message}`, {
      transactionId,
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
      body: request.body,
      query: request.query,
      params: request.params,
      error: errorDetails,
      stack: exception instanceof Error ? exception.stack : undefined,
    });

    // 클라이언트에 응답
    const errorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
      message: message,
      transactionId: transactionId,
      ...(process.env.NODE_ENV === 'development' && { details: errorDetails }),
    };

    response.status(status).json(errorResponse);
  }
}
