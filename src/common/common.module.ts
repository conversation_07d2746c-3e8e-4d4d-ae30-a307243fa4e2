import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';

import { CommonController } from './common.controller';
import { CommonService } from './common.service';
import { GlobalExceptionFilter } from './global-exception.filter';
import { GlobalResponseInterceptor } from './global-response.interceptor';

@Module({
  providers: [
    CommonService,
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: GlobalResponseInterceptor,
    },
  ],
  controllers: [CommonController],
})
export class CommonModule {}
