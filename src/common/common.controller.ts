import { Controller, Get } from '@nestjs/common';
import { ApiExcludeController } from '@nestjs/swagger';
import { CommonService } from './common.service';

@ApiExcludeController()
@Controller('')
export class CommonController {
  constructor(private readonly commonService: CommonService) {}

  @Get('version')
  getVersion() {
    return this.commonService.getVersion();
  }

  @Get('health')
  getHealth() {
    return this.commonService.getVersion();
  }

  @Get('health/liveness')
  liveness() {
    return this.commonService.liveness();
  }

  @Get('health/readiness')
  async readiness() {
    return this.commonService.readiness();
  }
}
