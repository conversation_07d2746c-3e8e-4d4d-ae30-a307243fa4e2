import { Injectable, NestInterceptor, Execution<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { TransactionContext } from './transaction.context';

@Injectable()
export class GlobalResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger(GlobalResponseInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const transactionContext = TransactionContext.getInstance();
    const transactionId = transactionContext.getTransactionId() || 'unknown';

    return next.handle().pipe(
      map((data) => {
        try {
          const response = context.switchToHttp().getResponse();
          const request = context.switchToHttp().getRequest();

          // POST 요청에 대해 상태 코드를 200으로 설정
          if (response.statusCode === 201) {
            response.status(200);
          }

          return data;
        } catch (error) {
          this.logger.error(
            `[${transactionId}] Error in response interceptor: ${error instanceof Error ? error.message : 'Unknown error'}`,
            error,
          );
          // 인터셉터에서 오류가 발생해도 원본 데이터를 반환
          return data;
        }
      }),
      catchError((error) => {
        // 에러를 로깅하지만 그대로 전파
        this.logger.error(`[${transactionId}] Error caught in response interceptor:`, error);
        return throwError(() => error);
      }),
    );
  }
}
