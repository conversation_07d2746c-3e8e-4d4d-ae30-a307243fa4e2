import { Injectable, ServiceUnavailableException } from '@nestjs/common';
import { DispatchApiService } from '../api/dispatch.api.service';
import { MapApiService } from '../api/map.api.service';
import { MeterApiService } from '../api/meter.api.service';
import { RouteApiService } from '../api/route.api.service';
import { SearchApiService } from '../api/search.api.service';

@Injectable()
export class CommonService {
  constructor(
    private searchApiService: SearchApiService,
    private routeApiService: RouteApiService,
    private meterApiService: MeterApiService,
    private dispatchApiService: DispatchApiService,
    private MapApiService: MapApiService,
  ) {}

  getVersion() {
    return this.readiness();
  }

  liveness() {
    return {
      status: 'ok',
      message: 'Server is running',
      timestamp: new Date().toISOString(),
    };
  }

  async readiness() {
    const services = [
      { name: 'Search', service: this.searchApiService },
      { name: 'Route', service: this.routeApiService },
      // { name: 'Meter', service: this.meterApiService },
      { name: 'Dispatch', service: this.dispatchApiService },
      // { name: 'Map', service: this.MapApiService },
    ];

    const results = await Promise.all(
      services.map(async ({ name, service }) => {
        const availableServers = service.getAvailableServers();
        const allServers = service.getAllServers();
        return {
          name,
          available: availableServers.length > 0,
          availableServers: availableServers.map((server) => server.url + '(' + server.weight + ')'),
          totalServers: allServers.length,
          healthyServers: availableServers.length,
        };
      }),
    );

    const unavailableServices = results.filter((result) => !result.available);

    if (unavailableServices.length > 0) {
      throw new ServiceUnavailableException({
        status: 'not ready',
        unavailableServices: unavailableServices.map((service) => service.name),
        serviceDetails: results,
      });
    }

    return {
      status: 'ok',
      services: results,
    };
  }
}
