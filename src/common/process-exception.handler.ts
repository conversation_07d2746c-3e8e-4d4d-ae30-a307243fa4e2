import { Logger } from '@nestjs/common';

export class ProcessExceptionHandler {
  private static readonly logger = new Logger(ProcessExceptionHandler.name);
  private static isShuttingDown = false;

  static setupGlobalErrorHandlers(): void {
    // 처리되지 않은 Promise 거부 처리
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      this.logger.error('🚨 Unhandled Promise Rejection detected!', {
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined,
        promise: promise.toString(),
      });

      // 서버를 종료하지 않고 계속 실행
      // process.exit(1); // 이 줄을 제거하여 서버가 다운되지 않도록 함
    });

    // 처리되지 않은 예외 처리
    process.on('uncaughtException', (error: Error) => {
      this.logger.error('🚨 Uncaught Exception detected!', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });

      // 심각한 오류의 경우에만 graceful shutdown
      if (this.isCriticalError(error)) {
        this.logger.error('Critical error detected, initiating graceful shutdown...');
        this.gracefulShutdown();
      } else {
        this.logger.warn('Non-critical error, continuing operation...');
      }
    });

    // SIGTERM 및 SIGINT 신호 처리 (graceful shutdown)
    process.on('SIGTERM', () => {
      this.logger.log('SIGTERM received, initiating graceful shutdown...');
      this.gracefulShutdown();
    });

    process.on('SIGINT', () => {
      this.logger.log('SIGINT received, initiating graceful shutdown...');
      this.gracefulShutdown();
    });

    // 다중 리스너 경고 제한 증가
    process.setMaxListeners(20);

    this.logger.log('🛡️ Global error handlers configured');
  }

  private static isCriticalError(error: Error): boolean {
    // 심각한 오류인지 판단하는 로직
    const criticalErrors = [
      'ENOSPC', // 디스크 공간 부족
      'ENOMEM', // 메모리 부족
      'MODULE_NOT_FOUND', // 필수 모듈 없음
    ];

    return criticalErrors.some((pattern) => error.message.includes(pattern) || error.name.includes(pattern));
  }

  private static gracefulShutdown(): void {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    this.logger.log('Initiating graceful shutdown...');

    // 새로운 요청 거부 후 기존 요청 완료를 위한 시간 제공
    setTimeout(() => {
      this.logger.log('Forcing shutdown after timeout');
      process.exit(1);
    }, 10000); // 10초 후 강제 종료

    // 애플리케이션별 정리 작업이 있다면 여기에 추가
    process.exit(0);
  }
}
