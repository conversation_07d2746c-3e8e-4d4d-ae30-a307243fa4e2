import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { json } from 'body-parser';
import * as fs from 'fs';
import * as yaml from 'js-yaml';
import { AppModule } from './app.module';
import { GlobalResponseInterceptor } from './common/global-response.interceptor';
import { ResponseTransformInterceptor } from './middleware/response-transform.interceptor';
import { ProcessExceptionHandler } from './common/process-exception.handler';
import { setupWinston } from './setup/setup.winston';

async function bootstrap() {
  // 전역 에러 핸들러 설정 (가장 먼저 실행)
  ProcessExceptionHandler.setupGlobalErrorHandlers();

  const app = await NestFactory.create(AppModule, {
    logger: setupWinston(),
  });

  app.enableCors(); //CORS 허용

  app.use(json({ limit: '50mb' }));

  // 인터셉터 실행 순서: GlobalResponseInterceptor -> ResponseTransformInterceptor
  app.useGlobalInterceptors(new GlobalResponseInterceptor(), new ResponseTransformInterceptor());

  const configService = app.get(ConfigService);

  const port = configService.get<string>('PORT') || 3000;

  const apiBaseUrl = configService.get<string>('API_BASE_URL');
  const serverUrl = apiBaseUrl || `http://localhost:${port}`;
  const mapUrl = configService.get<string>('MAP_URL') || serverUrl;

  const config = new DocumentBuilder()
    .setTitle('Logisteq LBS Core API')
    .setDescription('Location-based services API for North America region')
    .setVersion('1.0')
    .addServer(serverUrl)
    .addServer(mapUrl)
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  // Convert JSON to YAML
  const yamlString = yaml.dump(document);

  // Write YAML to file
  try {
    fs.writeFileSync('./swagger.yaml', yamlString, 'utf8');
  } catch (error) {
    console.warn('Failed to write swagger.yaml file:', error instanceof Error ? error.message : 'Unknown error');
  }

  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
}

// bootstrap 함수 실행 시 에러 처리
bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
